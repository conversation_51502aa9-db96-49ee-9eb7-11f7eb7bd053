import { Navigate, RouteObject } from "react-router-dom";
import ProtectedRoute from "./components/common/ProtectedRoute";
import MainLayout from "./components/layout/MainLayout";

// Auth pages
import LoginPage from "./pages/auth/LoginPage";
import TwoFactorPage from "./pages/auth/TwoFactorPage";
import ForgotPasswordPage from "./pages/auth/ForgotPasswordPage";

// App pages
import DashboardPage from "./pages/dashboard/DashboardPage";
import ResetPasswordPage from "./pages/auth/ResetPasswordPage";
import SystemPreferences from "./pages/preferences/SystemPreferences";
import VendorLoginPage from "./pages/auth/vendorLoginPage";
import EmailTemplates from "./pages/notification/notification";
import NotificationTemplate from "./pages/notification/edit-notification";
import NoData from "./pages/users/Nodata";
import SampleData2 from "./pages/users/sampleData2";
import SampleData3 from "./pages/users/sampleData3";
import BasicInformationPage from "./pages/vendor/basicInfo";
import NotificationTemplatePreview from "./pages/notification/previewTemplate";
import RolesManagementPage from "./pages/roles-management/RolesManagementPage";
import MasterDataManagementPage from "./pages/master-data-management/MasterDataManagementPage";
import ChangePasswordPage from "./pages/auth/ChangePasswordPage";
import VendorForgotPasswordPage from "./pages/auth/VendorForgotPasswordPage";
import VendorResetPasswordPage from "./pages/auth/VendorResetPasswordPage";
import VendorChangePasswordPage from "./pages/auth/VendorChangePasswordPage";
import ContactDetailsPage from "./pages/vendor/ContactDetailsPage";
import VendorTwoFactorPage from "./pages/auth/VendorTwofactorPage";
import VendorCompanyProfilePage from "./pages/vendor/CompanyProfileDetails";
import VendorCompanyProfileFullview from "./pages/users/VendorCompanyProfileFullview";
import VendorContactDetailsFullView from "./pages/users/VendorContactdetailsFullview";
import ChangeHistoryPage from "./pages/preferences/ChangeHistoryPage";
import VendorChangeHistoryPage from "./pages/vendor/VendorChangeHistory";
import VendorPMIContacts from "./pages/master-data-management/pmi/vendor-internal-contacts";
import PmiInternalHistory from "./pages/master-data-management/pmi/pmi-internal-contacts-history";
import VendorCompanyLocationsFullView from "./pages/users/VendorLocationFullview";
import PmiMapContactHistory from "./pages/master-data-management/pmi/pmi-vendor-mapping-history";
import VendorMapContactView from "./pages/master-data-management/pmi/vendor-mapcontact-view";

const routes: RouteObject[] = [
  // {
  //   path: '/',
  //   element: <Navigate to="/login" replace />,
  // },
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/vendor-login",
    element: <VendorLoginPage />,
  },
  {
    path: "/two-factor",
    element: <TwoFactorPage />,
  },
  {
    path: "/vendor-two-factor",
    element: <VendorTwoFactorPage />,
  },
  {
    path: "/change-password",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <ChangePasswordPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/vendor-change-password",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <VendorChangePasswordPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/forgot-password",

    element: <ForgotPasswordPage />,
  },
  {
    path: "/vendor-forgot-password",

    element: <VendorForgotPasswordPage />,
  },

  {
    path: "/reset-password",
    element: <ResetPasswordPage />,
  },
  {
    path: "/vendor-reset-password",
    element: <VendorResetPasswordPage />,
  },
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <DashboardPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/notification-preferences",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <EmailTemplates />
        </MainLayout>
      </ProtectedRoute>
    ),
  },

  {
    path: "/roles-management/*",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <RolesManagementPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/master-data-management/*",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <MasterDataManagementPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/system-preferences",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <SystemPreferences />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/system-preferences-change-history",
    element: (
      <ProtectedRoute>
        <ChangeHistoryPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/vendor-profile-change-history",
    element: (
      <ProtectedRoute>
        <VendorChangeHistoryPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/pmi-internalcontacts-change-history",
    element: (
      <ProtectedRoute>
        <PmiInternalHistory />
      </ProtectedRoute>
    ),
  },
  {
    path: "/pmi-mapcontacts-change-history/:vendor_id",
    element: (
      <ProtectedRoute>
        <PmiMapContactHistory />
      </ProtectedRoute>
    ),
  },
  {
    path: "/notification-template/edit/:id",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <NotificationTemplate />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/notification-template/preview/:id",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <NotificationTemplatePreview />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/vendor-information",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <BasicInformationPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/companyprofile-details/:id",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <VendorCompanyProfileFullview />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/pmi-contacts/:id",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <VendorPMIContacts />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/contact-details/:id",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <VendorContactDetailsFullView />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/location-details/:id",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <VendorCompanyLocationsFullView />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/pmi-contacts",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <VendorMapContactView />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/vendor-onboarding/contact-details",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <ContactDetailsPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/vendor-onboarding/company-details",
    element: (
      <ProtectedRoute>VendorCompanyProfilePage
        <MainLayout>
          <VendorCompanyProfilePage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/no-data",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <NoData />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/sample-data-2",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <SampleData2 />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "/sample-data-3",
    element: (
      <ProtectedRoute>
        <MainLayout>
          <SampleData3 />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: "*",
    element: <Navigate to="/login" replace />,
  },
];

export default routes;
