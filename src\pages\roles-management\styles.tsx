import { styled } from "@mui/material/styles";
import { Box } from "@mui/material";

export const StyledProfileNav = styled(Box)(({ theme }) => ({
  display: "flex",
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  position: "sticky",
  top: 0,
  zIndex: 1,
}));

export const StyledProfileNavItem = styled(Box)<{ active: number }>(
  ({ theme, active }) => ({
    padding: theme.spacing(2, 3),
    cursor: "pointer",
    borderBottom: active ? `2px solid ${theme.palette.primary.main}` : "2px solid transparent",
    color: active ? theme.palette.primary.main : theme.palette.text.secondary,
    fontWeight: active ? 600 : 400,
    transition: "all 0.2s ease-in-out",
    "&:hover": {
      color: theme.palette.primary.main,
      backgroundColor: theme.palette.action.hover,
    },
  })
);
