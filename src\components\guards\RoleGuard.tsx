import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAuthStore } from '../../store/auth';
import { useTranslation } from 'react-i18next';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: string[];
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

const RoleGuard: React.FC<RoleGuardProps> = ({ 
  children, 
  allowedRoles, 
  fallbackPath = '/dashboard',
  showAccessDenied = true 
}) => {
  const { user } = useAuthStore();
  const location = useLocation();
  const { t } = useTranslation();

  // Check if user has any of the allowed roles
  const hasAllowedRole = () => {
    if (!user || !user.role) return false;
    
    // If user has multiple roles, check if any of them are in allowedRoles
    if (Array.isArray(user.roles)) {
      return user.roles.some((role: any) => 
        allowedRoles.includes(role.role_name || role.name)
      );
    }
    
    // If user has a single role
    return allowedRoles.includes(user.role);
  };

  if (!hasAllowedRole()) {
    if (showAccessDenied) {
      return (
        <Box p={3} textAlign="center">
          <Typography variant="h6" color="error">
            {t("common.accessDenied")}
          </Typography>
          <Typography variant="body2" color="textSecondary" mt={1}>
            {t("common.insufficientPermissions")}
          </Typography>
        </Box>
      );
    } else {
      return <Navigate to={fallbackPath} state={{ from: location }} replace />;
    }
  }

  return <>{children}</>;
};

export default RoleGuard;
