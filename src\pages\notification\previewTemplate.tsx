import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input<PERSON><PERSON>l,
  <PERSON><PERSON>k<PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
} from "@mui/material";
import { useEffect, useState } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { getAllmailTemplates } from "../../services/notification/notification";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { useLanguageStore } from "../../store/language";

function NotificationTemplatePreview() {
  const { t, i18n } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const [title, setTitle] = useState("");
  const [subject, setSubject] = useState("");
  const [message_body, setContent] = useState("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  const { id } = useParams();
  const navigate = useNavigate();

  // Force re-render when language changes
  useEffect(() => {
    // This will trigger a re-render when language changes
  }, [currentLanguage, i18n.language]);

  const { data: existingPreferences } = useQuery({
    queryKey: ["getAllmailTemplates"],
    queryFn: async () => {
      const response = await getAllmailTemplates();
      return response.result;
    },
  });

  const matchedTemplate = Array.isArray(existingPreferences)
    ? existingPreferences.find((vendor: any) => String(vendor.id) === String(id))
    : null;

  useEffect(() => {
    if (matchedTemplate) {
      setTitle(matchedTemplate.notification_title || "");
      setSubject(matchedTemplate.subject || "");
      setContent(matchedTemplate.message_body || "");
    }
  }, [matchedTemplate]);

  return (
<Box sx={{ width: "100%", maxWidth: "1400px", mt: 4 }}>
      <Card elevation={3}>
        <CardContent>
          <Typography variant="h5" color="primary" gutterBottom>
            {t("notification.preview")} {t("notification.title")}
          </Typography>

          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <InputLabel shrink>{t("notification.notificationTitle")}</InputLabel>
              <Typography
                variant="body1"
                sx={{ mt: 1, p: 1, backgroundColor: "#f5f5f5", borderRadius: 1 }}
              >
                {title || "-"}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <InputLabel shrink>{t("notification.subject")}</InputLabel>
              <Typography
                variant="body1"
                sx={{ mt: 1, p: 1, backgroundColor: "#f5f5f5", borderRadius: 1 }}
              >
                {subject || "-"}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <InputLabel shrink>{t("notification.emailBody")}</InputLabel>
              <ReactQuill
                theme="snow"
                value={message_body}
                readOnly
                modules={{ toolbar: false }}
                style={{
                  backgroundColor: "#f9f9f9",
                  borderRadius: 4,
                  padding: 8,
                  minHeight: 200,
                }}
              />
            </Grid>
          </Grid>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-start",
              mt: 4,
            }}
          >
            <Button variant="outlined" color="primary" onClick={() => navigate(-1)}>
              {t("common.back")}
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <MuiAlert
          onClose={() => setSnackbarOpen(false)}
          severity="success"
          sx={{ width: "100%" }}
        >
          {t("notification.templateUpdated")}
        </MuiAlert>
      </Snackbar>
    </Box>
  );
}

export default NotificationTemplatePreview;
