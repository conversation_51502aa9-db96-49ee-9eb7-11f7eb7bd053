import api from "../httpClient/api";

export const getAllRolePermissions = async () => {
  const response = await api.get('/user/view-role-permissions');
  return response.data;
};

export const assignRolePermissions = async ({ roleId, data }: { roleId: number; data: { permissionIds: number[] } }) => {
  return api.patch(`/user/assign-permissions/${roleId}`, data);
};

export const removeRolePermissions = async (roleId: number) => {
  return api.delete(`/user/remove-permissions/${roleId}`);
};