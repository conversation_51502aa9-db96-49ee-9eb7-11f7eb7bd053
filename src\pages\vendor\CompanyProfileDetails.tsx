import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  MenuItem,
  TextField,
  Typography,
  Paper,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import api from "../../services/httpClient/api";
import VendorStepper from "../../components/steppedbar/VendorStepper";
import { useAuthStore } from "../../store/auth";
import { useSnackbar } from "../../utils/SnackbarProvider";

interface CompanyProfile {
  vendor: number;
  company_name: string;
  address: string;
  address2: string;
  postal_code: string;
  city: string;
  state_provice: string;
  country: string;
}

const VendorCompanyProfilePage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const { user } = useAuthStore(); // ✅ moved inside component
  const [currentStep, setCurrentStep] = useState(0); // 0 = Company Profile
  const [hasExistingProfile, setHasExistingProfile] = useState(false);
  const [recordId, setRecordId] = useState();
  const defaultProfile: CompanyProfile = {
    vendor: Number(user?.id) || 0,
    company_name: "",
    address: "",
    address2: "",
    postal_code: "",
    city: "",
    state_provice: "",
    country: "",
  };
  const [profile, setProfile] = useState<CompanyProfile>(defaultProfile);
  const { showMessage } = useSnackbar();
  useEffect(() => {
    const fetchCompanyProfile = async () => {
      try {
        const res = await api.get(`/user/company-profile-vendor/${user?.id}`);
        console.log(res, "resres");
        const company = res.data?.result?.[0];
        const vendorId = company?.vendor?.id;
        setRecordId(company.id);
        console.log("Company profile:", company.id);

        if (vendorId && typeof company === "object") {
          setHasExistingProfile(true);
          setProfile({
            vendor: vendorId,
            company_name: company.company_name || "",
            address: company.address || "",
            address2: company.address2 || "",
            postal_code: company.postal_code || "",
            city: company.city || "",
            state_provice: company.state_provice || "",
            country: company.country || "",
          });
        } else {
          setHasExistingProfile(false);
        }
      } catch (error) {
        console.error("Error fetching company profile:", error);
        setHasExistingProfile(false);
      }
    };

    if (user?.id) {
      fetchCompanyProfile();
    }
  }, [user?.id]); // dependency ensures it runs when user is defined

  const handleChange = (field: keyof CompanyProfile, value: string) => {
    setProfile((prev) => ({ ...prev, [field]: value }));
  };
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setLogoFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!logoFile) {
      showMessage("Please select a logo file to upload.", "warning");
      return;
    }

    const formData = new FormData();
    formData.append("file", logoFile); // The backend will save this as logo.extension

    try {
      setUploading(true);
      const response = await api.post(
        "/user/company-profile-upload-logo",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (response.data?.success) {
        showMessage("Logo uploaded successfully!", "success");
      } else {
        showMessage("Upload failed. Please try again.", "error");
      }
    } catch (error) {
      console.error("Error uploading logo:", error);
      showMessage("An error occurred while uploading the logo.", "error");
    } finally {
      setUploading(false);
    }
  };
  const handleSubmit = async () => {
    try {
      const response = await api.post(`/user/create-company-profile`, profile);

      if (response.data?.success) {
        showMessage("Company profile saved successfully!", "success");
       setTimeout(() => navigate("/vendor-onboarding/contact-details"), 1500);
      } else {
        showMessage(
          response.data?.message ||
            "Something went wrong while creating company profile.",
          "error"
        );
      }
    } catch (error: any) {
      console.error("Error saving company profile:", error);
      showMessage(
        error?.response?.data?.message ||
          "An error occurred while saving the company profile.",
        "error"
      );
    }
  };
  const handleUpdate = async () => {
    try {
      // Send only the specified payload fields
      const updatePayload = {
        company_name: profile.company_name,
        address: profile.address,
        address2: profile.address2,
        postal_code: profile.postal_code,
        city: profile.city,
        state_provice: profile.state_provice,
        country: profile.country,
        updated_by: String(user?.id),
      };

      const response = await api.post(
        `/user/update-company-profile/${recordId}`,
        updatePayload
      );

      if (response.data?.success) {
        showMessage("Company profile updated successfully!", "success");
        navigate("/vendor-onboarding/contact-details");
      } else {
        showMessage(response.data?.message || "Update failed.", "error");
      }
    } catch (error: any) {
      console.error("Error updating company profile:", error);
      showMessage(
        error?.response?.data?.message || "An error occurred during update.",
        "error"
      );
    }
  };

  return (
    <>
      <VendorStepper activeStep={currentStep} />

      <Box px={3} py={2}>
        <Typography variant="h6" gutterBottom>
          Company Profile
        </Typography>

        <Paper sx={{ padding: 3, borderRadius: 2, backgroundColor: "#f9f9f9" }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Company Name *"
                value={profile.company_name}
                onChange={(e) => handleChange("company_name", e.target.value)}
                fullWidth
                required
              />
            </Grid>

            {/* Logo Upload Placeholder */}
            <Grid item xs={12} sm={6}>
              <Typography>Upload Logo Supplier *</Typography>
              <Box
                sx={{
                  border: "2px dashed #ccc",
                  borderRadius: "8px",
                  padding: 2,
                  textAlign: "center",
                  cursor: "pointer",
                  position: "relative",
                }}
                onClick={() =>
                  document.getElementById("logo-upload-input")?.click()
                }
              >
                {logoFile ? logoFile.name : "Click Here & Upload Logo"}
                <input
                  id="logo-upload-input"
                  type="file"
                  accept="image/*"
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                />
              </Box>

              <Box mt={2}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleUpload}
                  disabled={uploading}
                >
                  {uploading ? "Uploading..." : "Submit"}
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Address *"
                value={profile.address}
                onChange={(e) => handleChange("address", e.target.value)}
                fullWidth
                multiline
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Address 2"
                value={profile.address2}
                onChange={(e) => handleChange("address2", e.target.value)}
                fullWidth
                multiline
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Postal Code *"
                value={profile.postal_code}
                onChange={(e) => handleChange("postal_code", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="City *"
                value={profile.city}
                onChange={(e) => handleChange("city", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="State / Province *"
                value={profile.state_provice}
                onChange={(e) => handleChange("state_provice", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Country *"
                select
                value={profile.country}
                onChange={(e) => handleChange("country", e.target.value)}
                fullWidth
              >
                <MenuItem value="Germany">Germany</MenuItem>
                <MenuItem value="India">India</MenuItem>
                <MenuItem value="USA">USA</MenuItem>
                {/* Add more countries as needed */}
              </TextField>
            </Grid>
          </Grid>
        </Paper>

        <Box display="flex" justifyContent="space-between" mt={3}>
          <Button
            variant="contained"
            onClick={() => {
              if (currentStep > 0) setCurrentStep(currentStep - 1);
            }}
          >
            Back
          </Button>

          {hasExistingProfile ? (
            <Button variant="contained" color="primary" onClick={handleUpdate}>
              Save & Next
            </Button>
          ) : (
            <Button variant="contained" color="primary" onClick={handleSubmit}>
              Save & Next
            </Button>
          )}
        </Box>
      </Box>
    </>
  );
};

export default VendorCompanyProfilePage;
