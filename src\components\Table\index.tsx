import {
  <PERSON><PERSON>,
  Checkbox,
  Chip,
  CircularProgress,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  Menu,
  MenuItem,
  Select,
  TableCell,
  TablePagination,
  TableSortLabel,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { Box } from "@mui/system";
import { StyledTable, StyledTableContainer, StyledTableLoader } from "./styles";
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { Clear, FilterList, Search } from "@mui/icons-material";
import { useMemo, useState } from "react";

const ROWS_PER_PAGE_OPTIONS = [10, 25, 50, 75, 100];

export type ColumnType = {
  key: string;
  title: string | React.ReactElement;
  render?: (item: any) => React.ReactElement | string | null | number | boolean;
  hide?: boolean;
  default?: boolean;
  width?: string;
  sort?: boolean;
  active?: boolean;
  direction?: "asc" | "desc";
  filterable?: boolean;
  searchable?: boolean;
};

type PaginationType = {
  totalCount: number;
  page: number;
  setPage: (page: number) => void;
  pageCount?: number;
  setPageCount?: (pageCount: number) => void;
};

type SelectionType = {
  selected: any[];
  setSelected: (selected: any[]) => void;
};

interface TableProps {
  columns: Array<ColumnType>;
  sx?: any;
  data: any[];
  loading?: boolean;
  onRowClick?: (v: any) => void;
  sortHandler?: (v: any) => void;
  pagination?: PaginationType | null;
  selection?: SelectionType;
  heading?: string;
  enableSearch?: boolean;
  enableFilter?: boolean;
  searchPlaceholder?: string;
}

function Table(props: TableProps) {
  const { t } = useTranslation();
  const {
    columns,
    data = [],
    sx,
    pagination,
    loading = false,
    onRowClick,
    sortHandler,
    selection,
    heading,
    enableSearch = false,
    enableFilter = false,
    searchPlaceholder,
  } = props;

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);

  const { selected, setSelected } = selection || {
    selected: [],
    setSelected: () => {},
  };

  // Filter, search, and sort data
  const filteredData = useMemo(() => {
    let result = [...data];

    // Apply search filter
    if (enableSearch && searchTerm) {
      const searchableColumns = columns.filter(col => col.searchable !== false);
      result = result.filter(item => {
        return searchableColumns.some(col => {
          const value = _.get(item, col.key);
          if (value == null) return false;
          return String(value).toLowerCase().includes(searchTerm.toLowerCase());
        });
      });
    }

    // Apply column filters
    if (enableFilter) {
      Object.entries(filters).forEach(([columnKey, filterValue]) => {
        if (filterValue && filterValue !== '') {
          result = result.filter(item => {
            const value = _.get(item, columnKey);
            if (value == null) return false;
            return String(value).toLowerCase().includes(String(filterValue).toLowerCase());
          });
        }
      });
    }

    // Apply sorting
    const activeColumn = columns.find(col => col.active);
    if (activeColumn && activeColumn.direction) {
      result.sort((a, b) => {
        const aValue = _.get(a, activeColumn.key);
        const bValue = _.get(b, activeColumn.key);

        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return 1;
        if (bValue == null) return -1;

        // Convert to strings for comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();

        // Handle dates
        if (activeColumn.key.includes('date') || activeColumn.key.includes('Date')) {
          const aDate = new Date(aValue);
          const bDate = new Date(bValue);
          if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
            return activeColumn.direction === 'asc'
              ? aDate.getTime() - bDate.getTime()
              : bDate.getTime() - aDate.getTime();
          }
        }

        // Handle numbers
        const aNum = Number(aValue);
        const bNum = Number(bValue);
        if (!isNaN(aNum) && !isNaN(bNum)) {
          return activeColumn.direction === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // Handle strings
        if (activeColumn.direction === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      });
    }

    return result;
  }, [data, searchTerm, filters, columns, enableSearch, enableFilter]);

  // Calculate paginated data for display (only for client-side pagination)
  const paginatedData = useMemo(() => {
    if (!pagination) return filteredData;

    // If we have server-side pagination (totalCount > filteredData.length),
    // use the data as-is since it's already paginated from the server
    if (pagination.totalCount > filteredData.length) {
      return filteredData;
    }

    // For client-side pagination, slice the filtered data
    const startIndex = pagination.page * (pagination.pageCount || 10);
    const endIndex = startIndex + (pagination.pageCount || 10);
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, pagination]);

  // Get unique values for filter options
  const getFilterOptions = (columnKey: string) => {
    const values = data.map(item => _.get(item, columnKey)).filter(Boolean);
    return [...new Set(values)].sort();
  };

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelected(e.target.checked ? paginatedData : []);
  };

  const handleSelect = (e: React.ChangeEvent<HTMLInputElement>, item: any) => {
    e.stopPropagation();
    if (e.target.checked) {
      setSelected([...selected, item]);
    } else {
      const filtered = selected.filter((v: any) => v?.id !== item?.id);
      setSelected(filtered);
    }
  };

  const handleRowClick = (item: any) => {
    if (!onRowClick) return;
    onRowClick(item);
  };



  const handlePageChange = (_: any, page: number) => {
    pagination?.setPage(page);
    setSelected([]);
  };

  const handleRowsPerPageChange = (e: any) => {
    if (pagination?.setPageCount) {
      pagination?.setPage(0);
      pagination?.setPageCount(+e.target.value);
      setSelected([]);
    }
  };

  // Search and filter handlers
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    pagination?.setPage(0); // Reset to first page when searching
  };

  const handleFilterChange = (columnKey: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [columnKey]: value
    }));
    pagination?.setPage(0); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    pagination?.setPage(0);
  };

  const handleFilterMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterMenuClose = () => {
    setFilterAnchorEl(null);
  };

  // Sort handler
  const handleCreateSortHandler = (column: ColumnType) => {
    if (sortHandler) {
      sortHandler(column);
    }
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const tableContainerStyles = {
    height: isMobile ? "auto" : "70vh",
    overflowX: "auto",
    overflowY: "auto",
    ...sx,
  };

  return (
    <StyledTableContainer sx={sx}>
      {/* Search and Filter Controls */}
      {(enableSearch || enableFilter) && (
        <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            {enableSearch && (
              <TextField
                size="small"
                placeholder={searchPlaceholder || t('table.searchPlaceholder')}
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 250 }}
              />
            )}

            {enableFilter && (
              <>
                <IconButton
                  onClick={handleFilterMenuOpen}
                  color={Object.keys(filters).length > 0 ? 'primary' : 'default'}
                >
                  <FilterList />
                </IconButton>

                {Object.keys(filters).length > 0 && (
                  <Button
                    size="small"
                    onClick={clearFilters}
                    startIcon={<Clear />}
                    variant="outlined"
                  >
                    {t('table.clearFilters')}
                  </Button>
                )}

                {/* Active Filters Display */}
                {Object.entries(filters).map(([key, value]) => {
                  if (!value) return null;
                  const column = columns.find(col => col.key === key);
                  return (
                    <Chip
                      key={key}
                      label={`${column?.title}: ${value}`}
                      onDelete={() => handleFilterChange(key, '')}
                      size="small"
                      variant="outlined"
                    />
                  );
                })}
              </>
            )}
          </Box>
        </Box>
      )}

      <div style={tableContainerStyles}>
        {heading && (
          <Typography variant="h5" textAlign="center" my={2}>
            {heading}
          </Typography>
        )}
        <StyledTable sx={{ tableLayout: "auto", minWidth: "100%" }}>
          <thead>
            <tr>
              {selection && paginatedData?.length > 0 && (
                <th style={{ width: 50 }}>
                  <Checkbox
                    color="secondary"
                    onChange={handleSelectAll}
                    checked={selected?.length === paginatedData?.length}
                  />
                </th>
              )}
              {columns.map((item) => {
                if (item.hide) return null;
                return (
                  <TableCell
                    component="th"
                    key={item.key}
                    sortDirection={false}
                    sx={{
                      minWidth: item.width || "120px",
                      whiteSpace: "nowrap",
                      padding: "16px 8px",
                    }}
                  >
                    <Box
                      display="flex"
                      alignItems="center"
                      sx={{
                        whiteSpace: "nowrap",
                      }}
                    >
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontWeight: 600,
                          whiteSpace: "nowrap",
                        }}
                      >
                        {item.title}
                      </Typography>
                      {item.sort && (
                        <TableSortLabel
                          active={item?.active}
                          direction={item.direction}
                          onClick={() => handleCreateSortHandler(item)}
                          sx={{
                            ml: 1,
                            "& .MuiTableSortLabel-icon": {
                              opacity: item?.active ? 1 : 0.3,
                              color: item?.active ? "#ffffff" : "rgba(255, 255, 255, 0.5)",
                              fontSize: "1.2rem",
                            },
                            "&:hover .MuiTableSortLabel-icon": {
                              opacity: 1,
                              color: "#ffffff",
                            },
                            "&.Mui-active": {
                              color: "#ffffff",
                              "& .MuiTableSortLabel-icon": {
                                color: "#ffffff",
                              },
                            },
                          }}
                        />
                      )}
                    </Box>
                  </TableCell>
                );
              })}
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((item, rowIndex) => (
              <tr
                key={rowIndex}
                style={{
                  backgroundColor: rowIndex % 2 === 0 ? "#fff" : "#f9f9f9",
                  cursor: onRowClick ? "pointer" : "default",
                }}
                onClick={() => handleRowClick(item)}
              >
                {selection && (
                  <td>
                    <Checkbox
                      onChange={(e) => handleSelect(e, item)}
                      color="secondary"
                      checked={Boolean(_.find(selected, { id: item?.id }))}
                    />
                  </td>
                )}
                {columns.map((col, colIndex) => {
                  if (col.hide) return null;
                  return (
                    <td
                      key={colIndex}
                      style={{
                        whiteSpace: "nowrap",
                        padding: "12px 8px",
                        minWidth: col.width || "120px",
                      }}
                    >
                      {col.render ? (
                        col.render(item)
                      ) : (
                        <Typography
                          variant="body2"
                          sx={{
                            whiteSpace: "nowrap",
                          }}
                        >
                          {_.get(item, col.key)}
                        </Typography>
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </StyledTable>
      </div>

      {/* Filter Menu */}
      {enableFilter && (
        <Menu
          anchorEl={filterAnchorEl}
          open={Boolean(filterAnchorEl)}
          onClose={handleFilterMenuClose}
          slotProps={{
            paper: {
              sx: { minWidth: 250, maxHeight: 400 }
            }
          }}
        >
          {columns
            .filter(col => col.filterable !== false && !col.hide)
            .map((column) => {
              const options = getFilterOptions(column.key);
              if (options.length === 0) return null;

              return (
                <MenuItem key={column.key} sx={{ flexDirection: 'column', alignItems: 'stretch' }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>{String(column.title)}</InputLabel>
                    <Select
                      value={filters[column.key] || ''}
                      onChange={(e) => handleFilterChange(column.key, e.target.value)}
                      label={String(column.title)}
                    >
                      <MenuItem value="">
                        <em>{t('table.all')}</em>
                      </MenuItem>
                      {options.map((option) => (
                        <MenuItem key={option} value={option}>
                          {String(option)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </MenuItem>
              );
            })}
        </Menu>
      )}

      {pagination && (
        <Box justifyContent="flex-end" display="flex">
          <TablePagination
            component="div"
            count={pagination.totalCount || filteredData.length}
            page={pagination.page || 0}
            onPageChange={handlePageChange}
            rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
            rowsPerPage={pagination.pageCount || 10}
            onRowsPerPageChange={handleRowsPerPageChange}
          />
        </Box>
      )}

      {loading && (
        <StyledTableLoader>
          <CircularProgress color="primary" />
        </StyledTableLoader>
      )}

      {!loading && !paginatedData.length && (
        <StyledTableLoader>
          <Typography variant="subtitle2" color="rgba(0,0,0,0.5)">
            {data.length === 0 ? t("table.noDataToDisplay") : t("table.noMatchingResults")}
          </Typography>
        </StyledTableLoader>
      )}
    </StyledTableContainer>
  );
}

export default Table;
