import api from "../httpClient/api";

export interface UoM {
  id?: number;
  uom_code: string;
  uom_name: string;
  uom_alias: string;
  isActive: boolean;
}

// Get all UoMs
export const getAllUoMs = async () => {
  const response = await api.get('/user/view-uom');
  return response.data;
};

// Get UoM by ID
export const getUoMById = async (id: number) => {
  const response = await api.get(`/user/uoms/${id}`);
  return response.data;
};

// Create new UoM
export const createUoM = async (uomData: Omit<UoM, 'id'>) => {
  try {
    const response = await api.post('/user/create-uom', uomData);
    return response.data;
  } catch (error) {
    console.error("Error creating UoM:", error);
    throw error;
  }
};

// Update UoM
export const updateUoM = async (id: number, uomData: Partial<UoM>) => {
  try {
    const response = await api.post(`/user/update-uom/${id}`, uomData);
    return response.data;
  } catch (error) {
    console.error("Error updating UoM:", error);
    throw error;
  }
};

// Delete UoM
export const deleteUoM = async (id: number) => {
  try {
    const response = await api.post(`/user/delete-uom/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting UoM:", error);
    throw error;
  }
};

// Toggle UoM active status
export const toggleUoMStatus = async (id: number, isActive: boolean) => {
  try {
    const response = await api.post(`/user/toggle-uom-status/${id}`, { isActive });
    return response.data;
  } catch (error) {
    console.error("Error toggling UoM status:", error);
    throw error;
  }
};
