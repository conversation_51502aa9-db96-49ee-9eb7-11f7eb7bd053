import { useEffect, useState } from 'react';
import { useSystemPreferencesStore } from '../store/systemPreferences';

export const useReactiveLogo = () => {
  const { logoUrl, getLogoUrl } = useSystemPreferencesStore();
  const [currentLogoUrl, setCurrentLogoUrl] = useState<string>('');

  useEffect(() => {
    const newLogoUrl = getLogoUrl();
    setCurrentLogoUrl(newLogoUrl);
  }, [logoUrl, getLogoUrl]);

  useEffect(() => {
    const initialLogoUrl = getLogoUrl();
    setCurrentLogoUrl(initialLogoUrl);
  }, []);

  return currentLogoUrl;
};

export const useReactiveFavicon = () => {
  const { faviconUrl, getFaviconUrl, updateFavicon } = useSystemPreferencesStore();
  const [currentFaviconUrl, setCurrentFaviconUrl] = useState<string>('');

  useEffect(() => {
    const newFaviconUrl = getFaviconUrl();
    setCurrentFaviconUrl(newFaviconUrl);
    updateFavicon(newFaviconUrl);
  }, [faviconUrl, getFaviconUrl, updateFavicon]);

  // Initialize on mount
  useEffect(() => {
    const initialFaviconUrl = getFaviconUrl();
    setCurrentFaviconUrl(initialFaviconUrl);
    updateFavicon(initialFaviconUrl);
  }, []);

  return currentFaviconUrl;
};

export const useSystemPreferencesReactive = () => {
  const logoUrl = useReactiveLogo();
  const faviconUrl = useReactiveFavicon();

  return {
    logoUrl,
    faviconUrl,
  };
};
