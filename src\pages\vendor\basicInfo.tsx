// import { Typo<PERSON>, <PERSON>, <PERSON>Field, Grid } from "@mui/material";
// import { useAuthStore } from "../../store/auth";
// import { getAllvendors } from "../../services/vendor/vendor";
// import { useQuery } from "@tanstack/react-query";
// import { useTranslation } from "react-i18next";

// const BasicInformationPage = () => {
//   const { t } = useTranslation();
//   const { user } = useAuthStore();

//   const { data: existingPreferences } = useQuery({
//     queryKey: ["getAllvendors"],
//     queryFn: async () => {
//       const response = await getAllvendors();
//       return response.result;
//     },
//   });
//   console.log(existingPreferences, "existingPreferences BasicInformationPage");

//   const matchedVendor =
//     existingPreferences && Array.isArray(existingPreferences)
//       ? existingPreferences.find(
//           (vendor: {

//             name: string;
//             email: string;

//           }) =>
//             vendor.email?.toLowerCase().trim() ===
//             user?.email?.toLowerCase().trim()
//         ) || null
//       : null;


//   const vendorName = matchedVendor?.name || "Admin";
//   const vendorEmail = matchedVendor?.email || "";



//   return (
//     <Box>
//       <Typography variant="h5" gutterBottom>
//         {t("vendor.basicInformation")}
//       </Typography>

//       <Grid container spacing={2} sx={{ mt: 2 }}>
//         <Grid item xs={12} sm={6}>
//           <TextField
//             label={t("vendor.vendorName")}
//             value={vendorName}
//             fullWidth
//             InputProps={{
//               readOnly: true,
//             }}
//             variant="outlined"
//           />
//         </Grid>
//         <Grid item xs={12} sm={6}>
//           <TextField
//             label={t("vendor.vendorEmail")}
//             value={vendorEmail}
//             fullWidth
//             InputProps={{
//               readOnly: true,
//             }}
//             variant="outlined"
//           />
//         </Grid>
//       </Grid>
//     </Box>
//   );
// };

// export default BasicInformationPage;

import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  MenuItem,
  TextField,
  Typography,
  Paper,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import api from "../../services/httpClient/api";
import VendorStepper from "../../components/steppedbar/VendorStepper";
import { useAuthStore } from "../../store/auth";

interface CompanyProfile {
  vendor: number;
  company_name: string;
  address: string;
  address2: string;
  postal_code: string;
  city: string;
  state_provice: string;
  country: string;
}

const VendorCompanyProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore(); // ✅ moved inside component
  const [currentStep, setCurrentStep] = useState(0); // 0 = Company Profile
  const defaultProfile: CompanyProfile = {
    vendor: Number(user?.id) || 0,
    company_name: "",
    address: "",
    address2: "",
    postal_code: "",
    city: "",
    state_provice: "",
    country: "",
  };
  const [profile, setProfile] = useState<CompanyProfile>(defaultProfile);

useEffect(() => {
  const fetchCompanyProfile = async () => {
    try {
      const res = await api.get("/user/company-profiles");

      const company = res.data?.result?.[0];
      if (company && typeof company === "object") {
        setProfile({
          vendor: company.vendor?.id || Number(user?.id) || 0,
          company_name: company.company_name || "",
          address: company.address || "",
          address2: company.address2 || "",
          postal_code: company.postal_code || "",
          city: company.city || "",
          state_provice: company.state_provice || "",
          country: company.country || "",
        });
      }
    } catch (error) {
      console.error("Error fetching company profile:", error);
    }
  };

  fetchCompanyProfile();
}, []);

  const handleChange = (field: keyof CompanyProfile, value: string) => {
    setProfile((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    try {
      await api.post("/user/create-company-profile", profile);
      alert("Company profile saved successfully!");
      navigate("/vendor-onboarding/contact-details"); // Replace with your next step path
    } catch (error) {
      console.error("Error saving company profile:", error);
    }
  };

  return (
    <>
      <VendorStepper activeStep={currentStep} />

      <Box px={3} py={2}>
        <Typography variant="h6" gutterBottom>
          Company Profile
        </Typography>

        <Paper sx={{ padding: 3, borderRadius: 2, backgroundColor: "#f9f9f9" }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Company Name *"
                value={profile.company_name}
                onChange={(e) => handleChange("company_name", e.target.value)}
                fullWidth
                required
              />
            </Grid>

            {/* Logo Upload Placeholder */}
            <Grid item xs={12} sm={6}>
              <Typography>Upload Logo Supplier *</Typography>
              <Box
                sx={{
                  border: "2px dashed #ccc",
                  borderRadius: "8px",
                  padding: 2,
                  textAlign: "center",
                }}
              >
                Click Here & Upload Logo
              </Box>
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Address *"
                value={profile.address}
                onChange={(e) => handleChange("address", e.target.value)}
                fullWidth
                multiline
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Address 2"
                value={profile.address2}
                onChange={(e) => handleChange("address2", e.target.value)}
                fullWidth
                multiline
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Postal Code *"
                value={profile.postal_code}
                onChange={(e) => handleChange("postal_code", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="City *"
                value={profile.city}
                onChange={(e) => handleChange("city", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="State / Province *"
                value={profile.state_provice}
                onChange={(e) => handleChange("state_provice", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Country *"
                select
                value={profile.country}
                onChange={(e) => handleChange("country", e.target.value)}
                fullWidth
              >
                <MenuItem value="Germany">Germany</MenuItem>
                <MenuItem value="India">India</MenuItem>
                <MenuItem value="USA">USA</MenuItem>
                {/* Add more countries as needed */}
              </TextField>
            </Grid>
          </Grid>
        </Paper>

        <Box display="flex" justifyContent="space-between" mt={3}>
          <Button
            variant="contained"
            onClick={() => {
              if (currentStep > 0) setCurrentStep(currentStep - 1); // Go back
            }}
          >
            Back
          </Button>
          <Button variant="contained" color="primary" onClick={handleSubmit}>
            Save & Next
          </Button>
        </Box>
        
      </Box>
    </>
  );
};

export default VendorCompanyProfilePage;
