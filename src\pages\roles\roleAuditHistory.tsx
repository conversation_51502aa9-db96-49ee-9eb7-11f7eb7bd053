import { useTranslation } from "react-i18next";
import { useLanguageStore } from "../../store/language";
import { useEffect, useMemo, useState } from "react";
import { useAuthStore } from "../../store/auth";
import { useQuery } from "@tanstack/react-query";
import { getAllGlobalSystemPreferences } from "../../services/preferences/preferences";
import Table, { ColumnType } from "../../components/Table";
import {getRolesAudit } from "../../services/roles/roles";
import Loader from "../../utils/Loader";
import sortHandler from "../../components/Table/sortHandler";
import { Box, Typography } from "@mui/material";
import _ from "lodash";
import { formatDate } from "../../utils/dateFormats";




function RoleAuditHistory() {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
    const [page, setPage] = useState<number>(0);
  const [pageCount, setPageCount] = useState<number>(10);
  const [totalCount] = useState<number>(0);
  const [sort, setSort] = useState<any>({});

  const { user } = useAuthStore();
  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });

  const { data: rolesData, isLoading } = useQuery({
    queryKey: ["roles", page, pageCount, sort],
    queryFn: async () => {
      const response = await getRolesAudit();
      const allRoles = response.result || [];
      // Filter roles based on current tab's userType
      return allRoles
    },
  });

  const defaultColumns: Array<ColumnType> = useMemo(
    () => [
        {
        title: t("roles.userName"),
        key: "user_name",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("roles.roleName"),
        key: "role_name",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("roles.ChangedBy"),
        key: "updatedBy",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("roles.date"),
        key: "createdAt",
        sort: true,
        filterable: true,
        render: (row) => {
          const dateFormat =
            preferences?.result[0]?.date_format || "YYYY-MM-DD";
          return <span>{formatDate(row.createdAt, dateFormat)}</span>;
        },
      },
      {
        title: t("common.actions"),
        key: "action",
        searchable: false,
        filterable: false,
      
      },
    ],
    [t, currentLanguage, preferences]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };


  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return (
    <>
    

        <Table
         sx={{height:'auto'}}
          columns={columns}
          data={rolesData || []}
          sortHandler={handleSort}
          pagination={{
            totalCount,
            pageCount,
            setPageCount,
            page,
            setPage,
          }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />

    
    </>
  );
}

export default RoleAuditHistory;
