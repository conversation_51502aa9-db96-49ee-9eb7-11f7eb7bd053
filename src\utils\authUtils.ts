
export const publicRoutes = ['/login', '/two-factor', '/forgot-password', '/reset-password','/vendor-login'];

/**
 * @param path
 * @returns
 */
export const isPublicRoute = (path: string): boolean => {
  return publicRoutes.some(route => path.startsWith(route));
};

/**
 * @param path - The current path
 * @returns boolean - True if the path is an authentication route
 */
export const isAuthRoute = (path: string): boolean => {
  return path === '/login' || path === '/two-factor' || path === '/forgot-password' || path === '/reset-password' || path === '/vendor-login';
};

export const getUserRole = (): string | null => {
  try {
    const storedAuth = JSON.parse(localStorage.getItem('auth-storage') || '{}');
    // First check if role is directly in state
    if (storedAuth?.state?.role) {
      return storedAuth.state.role;
    }
    // Then check if it's in the user object
    if (storedAuth?.state?.user?.role) {
      return storedAuth.state.user.role;
    }
    return null;
  } catch {
    return null;
  }
};

export const clearPartialAuthState = (): void => {
  localStorage.removeItem('twoFactorEmail');
  localStorage.removeItem('isVendorLogin'); // Clear vendor login flag

  const authData = localStorage.getItem('auth-storage');
  if (authData) {
    const parsedData = JSON.parse(authData);
    if (parsedData?.state && !parsedData.state.hasCompleted2FA) {
      localStorage.removeItem('auth-storage');
    }
  }
};
