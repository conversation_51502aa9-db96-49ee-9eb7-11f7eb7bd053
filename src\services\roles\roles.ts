import api from "../httpClient/api";

export interface Role {
  userType: string;
  id?: number;
  role_name: string;
  description: string;
  type: 'user' | 'vendor';
  createdAt?: string;
  updatedAt?: string;
}

// Get all roles
export const getAllRoles = async () => {
  const response = await api.get('/user/view-roles');
  return response.data;
};

export const getRolesAudit   = async () => {
  const response = await api.get('/user/view-role-audits');
  return response.data;
};
// Get roles by type (user or vendor)
export const getRolesByType = async (type: 'user' | 'vendor') => {
  const response = await api.get(`/user/roles?type=${type}`);
  return response.data;
};

// Get role by ID
export const getRoleById = async (id: number) => {
  const response = await api.get(`/user/view-roles/${id}`);
  return response.data;
};

// Create new role
export const createRole = async (roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const response = await api.post('/user/create-role', roleData);
    return response.data;
  } catch (error) {
    console.error("Error creating role:", error);
    throw error;
  }
};

// Update role
export const updateRole = async (id: number, roleData: Partial<Role>) => {
  try {
    const response = await api.post(`/user/update-role/${id}`, roleData);
    return response.data;
  } catch (error) {
    console.error("Error updating role:", error);
    throw error;
  }
};

// Delete role
export const deleteRole = async (id: number) => {
  try {
    const response = await api.post(`/user/delete-role/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting role:", error);
    throw error;
  }
};
