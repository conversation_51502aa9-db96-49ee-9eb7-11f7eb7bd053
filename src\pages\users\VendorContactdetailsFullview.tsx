import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  MenuItem,
  TextField,
  Typography,
  Paper,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { useAuthStore } from "../../store/auth";
import VendorStepper from "../../components/steppedbar/VendorStepper";
import { getAllRoles } from "../../services/roles/roles";
import { getAllvendors } from "../../services/vendor/vendor";
import api from "../../services/httpClient/api";
import { useSnackbar } from "../../utils/SnackbarProvider";
import { getContactDetailsById } from "../../services/vendorcontact/vendor-contact";

interface ContactDetail {
  id?: number;
  vendor: number | { id: number }; // handle both formats
  contact_type: string;
  title: string;
  first_name: string;
  last_name: string;
  email: string;
  office_number: string;
  mobile_number: string;
  gender: string;
}

const VendorContactDetailsFullView: React.FC = () => {
  const { user } = useAuthStore();
  const params = useParams();
  const { id } = params;
  const navigate = useNavigate();
  const { showMessage } = useSnackbar();
  const [currentStep, setCurrentStep] = useState(1);
  const [showEmptyError, setShowEmptyError] = useState(false);
  const emptyContact: ContactDetail = {
    vendor: Number(id) || 0,
    contact_type: "",
    title: "",
    first_name: "",
    last_name: "",
    email: "",
    office_number: "",
    mobile_number: "",
    gender: "",
  };

  const [contactList, setContactList] = useState<ContactDetail[]>([
    emptyContact,
  ]);

  const { data: rolesData = [] } = useQuery({
    queryKey: ["roles", user?.role],
    queryFn: async () => {
      if (!user) return [];
      const response = await getAllRoles();
      return (
        response.result?.filter((role: any) => role.userType === user.role) ||
        []
      );
    },
    enabled: !!user,
  });

  const { data: existingPreferences } = useQuery({
    queryKey: ["getAllvendors"],
    queryFn: getAllvendors,
  });

  const matchedVendor =
    existingPreferences && Array.isArray(existingPreferences)
      ? existingPreferences.find(
          (vendor: { name: string; email: string }) =>
            vendor.email?.toLowerCase().trim() ===
            user?.email?.toLowerCase().trim()
        ) || null
      : null;

  const { data: contactListData } = useQuery({
    queryKey: ["contact-details-vendor", id],
    queryFn: () => getContactDetailsById(Number(id)),
    enabled: !!id,
  });
  useEffect(() => {
    if (Array.isArray(contactListData?.result)) {
      setContactList(contactListData.result);
      setShowEmptyError(contactListData.result.length === 0);
    }
  }, [contactListData]);

  const handleInputChange = (index: number, field: string, value: string) => {
    const updatedContacts = [...contactList];
    updatedContacts[index] = {
      ...updatedContacts[index],
      [field]: value,
    };
    setContactList(updatedContacts);
  };

  const handleAddNewContact = () => {
    setContactList([...contactList, { ...emptyContact }]);
  };

  const handleDeleteContact = (index: number) => {
    if (contactList.length === 1) return;
    const updatedList = contactList.filter((_, i) => i !== index);
    setContactList(updatedList);
  };

  const handleSubmit = async () => {
    try {
      const payload = {
        updates: contactList.map((contact) => ({
          ...contact,
          vendor: typeof contact.vendor === "object" ? contact.vendor.id : id,
        })),
      };

      const response = await api.post(
        `/user/update-contact-details-vendor`,
        payload
      );
      if (contactList.length === 0) {
        showMessage(
          "Please add at least one contact before proceeding.",
          "error"
        );
        return;
      }
      if (response.data?.success) {
        showMessage("Contacts saved successfully!", "success");
        setTimeout(() => navigate(`/location-details/${id}`), 1500);
      } else {
        showMessage("Something went wrong while saving contacts.", "error");
      }
    } catch (error: any) {
      console.error("Error saving contacts:", error);
      showMessage(
        error?.response?.data?.message ||
          "An error occurred while saving contacts.",
        "error"
      );
    }
  };

  return (
    <>
      <VendorStepper activeStep={currentStep} />
      {contactList.length === 0 && (
        <Box my={2}>
          <Typography color="error" variant="subtitle1">
            No contact details available. Please add at least one contact.
          </Typography>
        </Box>
      )}
      <Box px={3} py={2}>
        <Typography variant="h6" gutterBottom>
          Contact Details
        </Typography>

        {Array.isArray(contactList) &&
          contactList.map((contact, index) => {
            return (
              <Paper
                key={index}
                sx={{
                  padding: 3,
                  borderRadius: 2,
                  mb: 3,
                  backgroundColor: "#f9f9f9",
                }}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Contact Type *"
                      value={contact.contact_type || ""}
                      onChange={(e) =>
                        handleInputChange(index, "contact_type", e.target.value)
                      }
                      fullWidth
                      select
                    >
                      {rolesData.map((role: any) => (
                        <MenuItem key={role.id} value={role.role_name}>
                          {role.role_name}
                        </MenuItem>
                      ))}
                    </TextField>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Title *"
                      value={contact.title || ""}
                      onChange={(e) =>
                        handleInputChange(index, "title", e.target.value)
                      }
                      fullWidth
                      select
                    >
                      <MenuItem value="Mr">Mr</MenuItem>
                      <MenuItem value="Ms">Ms</MenuItem>
                      <MenuItem value="Mrs">Mrs</MenuItem>
                    </TextField>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="First Name *"
                      value={
                        contact.first_name || matchedVendor?.first_name || ""
                      }
                      onChange={(e) =>
                        handleInputChange(index, "first_name", e.target.value)
                      }
                      fullWidth
                      inputProps={{ maxLength: 50 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Last Name *"
                      value={
                        contact.last_name || matchedVendor?.last_name || ""
                      }
                      onChange={(e) =>
                        handleInputChange(index, "last_name", e.target.value)
                      }
                      fullWidth
                      inputProps={{ maxLength: 50 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      Gender *
                    </Typography>
                    <RadioGroup
                      row
                      value={contact.gender || ""}
                      onChange={(e) =>
                        handleInputChange(index, "gender", e.target.value)
                      }
                    >
                      <FormControlLabel
                        value="male"
                        control={<Radio />}
                        label="Male"
                      />
                      <FormControlLabel
                        value="female"
                        control={<Radio />}
                        label="Female"
                      />
                    </RadioGroup>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Email *"
                      value={contact.email || ""}
                      onChange={(e) =>
                        handleInputChange(index, "email", e.target.value)
                      }
                      fullWidth
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Office Phone *"
                      type="number"
                      value={contact.office_number || ""}
                      onChange={(e) =>
                        handleInputChange(
                          index,
                          "office_number",
                          e.target.value
                        )
                      }
                      fullWidth
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Mobile Phone *"
                      type="number"
                      value={contact.mobile_number || ""}
                      onChange={(e) =>
                        handleInputChange(
                          index,
                          "mobile_number",
                          e.target.value
                        )
                      }
                      fullWidth
                    />
                  </Grid>
                </Grid>

                <Box display="flex" justifyContent="flex-end" mt={2}>
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={() => handleDeleteContact(index)}
                    disabled={contactList.length === 1}
                  >
                    Delete
                  </Button>
                </Box>
              </Paper>
            );
          })}

        {/* <Box display="flex" justifyContent="flex-end" mb={2}>
          <Button variant="contained" onClick={handleAddNewContact}>
            New Contact
          </Button>
        </Box> */}

        <Box display="flex" justifyContent="space-between">
          <Button
            variant="contained"
            onClick={() => navigate(`/companyprofile-details/${id}`)}
          >
            Back
          </Button>

          <Button variant="contained" color="primary" onClick={handleSubmit}>
            Save & Next
          </Button>
        </Box>
      </Box>
    </>
  );
};

export default VendorContactDetailsFullView;
