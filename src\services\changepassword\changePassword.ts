import api from "../httpClient/api";

export interface ChangePasswordPayload {
  email: string;
  password: string;
}

export interface UpdatePasswordPayload {
  code: string;
  password: string;
  email:string;
}
export const ChangePassword = async (passwordData: ChangePasswordPayload) => {
  try {
    const response = await api.post('/user/change-password', passwordData);
    return response.data;
  } catch (error) {
    console.error("Error changing password:", error);
    throw error;
  }
};

export const ChangePasswordVendor = async (passwordData: ChangePasswordPayload) => {
  try {
    const response = await api.post('/user/change-password-vendor', passwordData);
    return response.data;
  } catch (error) {
    console.error("Error changing password:", error);
    throw error;
  }
};

export const updatePassword = async (passwordData: UpdatePasswordPayload) => {
  try {
    const response = await api.post('/user/update-password', passwordData);
    return response.data;
  } catch (error) {
    console.error("Error updating password:", error);
    throw error;
  }
};

export const updatePasswordVendor = async (passwordData: UpdatePasswordPayload) => {
  try {
    const response = await api.post('/user/update-password-vendor', passwordData);
    return response.data;
  } catch (error) {
    console.error("Error updating password:", error);
    throw error;
  }
};