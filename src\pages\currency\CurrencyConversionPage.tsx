import { useState, useEffect, useMemo } from "react";
import { Add, Delete, Edit } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  Box,
  Checkbox,
  FormControlLabel,
  IconButton,
} from "@mui/material";
import _ from "lodash";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import { SortDirection } from "../../data/constants";
import CloseIcon from "@mui/icons-material/Close";
import { toggleUoMStatus, UoM } from "../../services/uom/uom";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import Loader from "../../utils/Loader";
import {
  createCurrency,
  Currency,
  deleteCurrencyCode,
  getAllCurrencyCodes,
  updateCurrencyCode,
} from "../../services/currency/currencyConversionService";
import api from "../../services/httpClient/api";
const API_KEY = import.meta.env.VITE_CURRENCY_FREAKS_API_KEY;
const API_URL = `https://api.currencyfreaks.com/latest?apikey=${API_KEY}`;
function CurrencyExchangePage() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { currentLanguage } = useLanguageStore();
  const { user } = useAuthStore();
  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedUoM, setSelectedUoM] = useState<Currency | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [uomToDelete, setUoMToDelete] = useState<Currency | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [updatedCurrencies, setUpdatedCurrencies] = useState<any[]>([]); // NEW
  const [formData, setFormData] = useState({
    currency_code: "",
    currency_value: "",
    isActive: true,
  });

  const [formErrors, setFormErrors] = useState({
    currency_code: "",
    currency_value: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error",
  });

  // Pagination and sorting state
  const [page, setPage] = useState(0);
  const [pageCount, setPageCount] = useState(10);
  const [sort, setSort] = useState<any>({});

  // Fetch UoMs
  const { data: currencyResponse, isCurrencyLoading } = useQuery({
    queryKey: ["uoms", page, pageCount, sort],
    queryFn: getAllCurrencyCodes,
  });

  const currencyData = currencyResponse?.result || [];
  //
  const totalCount = currencyResponse?.totalCount || 0;

  // Create createCurrencyMutation
  const createCurrencyMutation = useMutation({
    mutationFn: createCurrency,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      setSnackbar({
        open: true,
        message: t("pricing.pricingCreated"),
        severity: "success",
      });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });
  const handleRefreshRates = async () => {
    try {
      setIsLoading(true);

      const { data: latestRatesData } = await api.get(API_URL);
      const latestRates = latestRatesData.rates;

      const eurRate = parseFloat(latestRates["EUR"]);
      if (!eurRate || isNaN(eurRate)) {
        throw new Error("EUR rate not found in external API data.");
      }

      const currencies = currencyData || [];

      if (!currencies.length) {
        alert("No currencies found in the system to update.");
        return;
      }

      const updatePromises = currencies.map((currency: any) => {
        const currencyCode = currency.currency_code;
        const matchingRateRaw = latestRates[currencyCode];

        if (matchingRateRaw) {
          const matchingRate = parseFloat(matchingRateRaw);
          if (isNaN(matchingRate)) {
            console.warn(`Invalid rate for ${currencyCode}:`, matchingRateRaw);
            return Promise.resolve();
          }

          const rateInEurBase = matchingRate / eurRate;

          const roundedValue = rateInEurBase.toFixed(4);

          return api.post("/user/update-currency-code", {
            updates: [
              {
                id: currency.id,
                currency_value: roundedValue,
              },
            ],
          });
        }

        return Promise.resolve(); // Skip if no matching rate
      });

      const results = await Promise.allSettled(updatePromises);

      const failedUpdates = results.filter(
        (result) => result.status === "rejected"
      );
      if (failedUpdates.length) {
        console.warn(`${failedUpdates.length} currency updates failed.`);
      }

      // 4️⃣ Refresh data
      queryClient.invalidateQueries({ queryKey: ["uoms"] });

      setSnackbar({
        open: true,
        message: t("pricing.currencyRatesUpdatedSuccessfully"),
        severity: "success",
      });
    } catch (error) {
      console.error("Error updating currency rates:", error);
      setSnackbar({
        open: true,
        message: t("pricing.failedToUpdateCurrencyRates"),
        severity: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  
  const updateUoMMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Currency> }) =>
      updateCurrencyCode([
        {
          id,
          currency_code: data.currency_code ?? "",
          currency_value: data.currency_value ?? "",
          isActive: data.isActive ?? true,
        },
      ]),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      setSnackbar({
        open: true,
        message: t("pricing.pricingdetailsUpdatedSuccessfully"),
        severity: "success",
      });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });
const sortedData = [...(updatedCurrencies.length > 0 ? updatedCurrencies : currencyData || [])].sort((a, b) => {
  const aCode = typeof a.currency_code === "string" ? a.currency_code : "";
  const bCode = typeof b.currency_code === "string" ? b.currency_code : "";
  return aCode.localeCompare(bCode);
});

  // Delete UoM mutation
  const deleteUoMMutation = useMutation({
    mutationFn: deleteCurrencyCode,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      setSnackbar({
        open: true,
        message: t("pricing.pricingDelete"),
        severity: "success",
      });
      setDeleteDialogOpen(false);
      setUoMToDelete(null);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setSnackbar({
        open: true,
        message: errorMessage,
        severity: "error",
      });
    },
  });

  // Toggle status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
      toggleUoMStatus(id, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setSnackbar({
        open: true,
        message: errorMessage,
        severity: "error",
      });
    },
  });

  // Table columns
  const defaultColumns: ColumnType[] = useMemo(
    () => [
      {
        title: t("pricing.CuurencyCode"),
        key: "currency_code",
        // sort: true,
        searchable: true,
        filterable: true,
        width: "150px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("pricing.currencyValue"),
        key: "currency_value",
        // sort: true,
        searchable: true,
        filterable: true,
        width: "200px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("pricing.status"),
        key: "isActive",
        width: "120px",
        direction: SortDirection.ASC,
        active: false,
        render: (item: UoM) =>
          item.isActive ? (
            <Checkbox checked color="primary" disabled />
          ) : (
            <Checkbox
              icon={<CloseIcon style={{ color: "grey" }} />}
              checkedIcon={<CloseIcon style={{ color: "grey" }} />}
              disabled
              checked
            />
          ),
      },
      {
        title: t("common.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        width: "120px",
        render: (item: Currency) => (
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => handleEdit(item)}
              color="primary"
            >
              <Edit fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => handleDeleteClick(item)}
              color="error"
            >
              <Delete fontSize="small" />
            </IconButton>
          </Box>
        ),
      },
    ],
    [t, currentLanguage, toggleStatusMutation]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const validateForm = () => {
    const errors = {
      currency_code: "",
      currency_value: "",
    };

    if (!formData.currency_code.trim()) {
      errors.currency_code = t("uom.uomCodeRequired");
    }

    setFormErrors(errors);
    return !Object.values(errors).some((error) => error !== "");
  };

  const handleSubmit = async () => {
    setGeneralError("");

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const currencyValue =
        formData.currency_value !== null &&
        formData.currency_value !== undefined
          ? String(formData.currency_value).trim()
          : null;
      const payload: any = {
        currency_code: formData.currency_code.trim(),
        currency_value: currencyValue || null,
        isActive: formData.isActive,
      };

      if (editMode && selectedUoM) {
        updateUoMMutation.mutate({
          id: selectedUoM.id!,
          data: {
            currency_code: formData.currency_code.trim(),
            currency_value: formData.currency_value?.toString().trim() ?? "",
            isActive: formData.isActive,
          },
        });
      } else {
        createCurrencyMutation.mutate([payload]);
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleEdit = (item: Currency) => {
    setSelectedUoM(item);
    setFormData({
      currency_code: item.currency_code,
      currency_value: item.currency_value,
      isActive: item.isActive,
    });
    setEditMode(true);
    setOpen(true);
  };

  const handleDeleteClick = (item: Currency) => {
    setUoMToDelete(item);
    setDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      currency_code: "",
      currency_value: "",
      isActive: true,
    });
    setFormErrors({
      currency_code: "",
      currency_value: "",
    });
    setSelectedUoM(null);
    setEditMode(false);
  };

  const handleDeleteConfirm = () => {
    if (uomToDelete) {
      deleteUoMMutation.mutate(uomToDelete.id!);
    }
  };


  const handleCreateNew = () => {
    resetForm();
    setOpen(true);
  };

  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Box m={2}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h4">{t("pricing.title")}</Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateNew}
          >
            {t("pricing.createPricing")}
          </Button>
        </Box>
        <Button
          onClick={handleRefreshRates}
          disabled={isLoading}
          variant="contained"
          color="primary"
          sx={{ mb: 2 }}
        >
          {isLoading ? "Refreshing..." : "Refresh Rates"}
        </Button>

        <Table
         sx={{height:'auto'}}
          columns={columns}
          data={sortedData || []}
          sortHandler={handleSort}
          pagination={{
            totalCount,
            pageCount,
            setPageCount,
            page,
            setPage,
          }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isCurrencyLoading}
        />
      </Box>

      {/* UoM Create/Edit Dialog */}
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {editMode ? t("pricing.editPricing") : t("pricing.createPricing")}
        </DialogTitle>
        <DialogContent dividers>
          {generalError && <Alert severity="error">{generalError}</Alert>}

          <TextField
            autoFocus
            margin="dense"
            label={t("pricing.currencyCode")}
            fullWidth
            variant="outlined"
            value={formData.currency_code}
            onChange={(e) =>
              setFormData({ ...formData, currency_code: e.target.value })
            }
            error={!!formErrors.currency_code}
            helperText={formErrors.currency_code}
            // placeholder={t("uom.uomCodePlaceholder")}
            sx={{ mb: 2 }}
          />
          {/* 
          <TextField
            margin="dense"
            label={t("pricing.cuurencyValue")}
            fullWidth
            variant="outlined"
            disabled
            value={formData.currency_value}
            onChange={(e) =>
              setFormData({ ...formData, currency_value: e.target.value })
            }
            error={!!formErrors.currency_value}
            helperText={formErrors.currency_value}
            // placeholder={t("uom.uomNamePlaceholder")}
            sx={{ mb: 2 }}
          /> */}

          <FormControlLabel
            control={
              <Checkbox
                checked={formData.isActive}
                onChange={(e) =>
                  setFormData({ ...formData, isActive: e.target.checked })
                }
                color="primary"
              />
            }
            label={t("uom.isActive")}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)} color="inherit">
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting}
          >
            {isSubmitting
              ? t("common.loading")
              : editMode
              ? t("common.update")
              : t("common.create")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t("uom.deleteUom")}</DialogTitle>
        <DialogContent>
          <Typography>{t("uom.confirmDeleteUom")}</Typography>
          {uomToDelete && (
            <Typography variant="body2" sx={{ mt: 1, fontWeight: "bold" }}>
              {uomToDelete.currency_value} ({uomToDelete.currency_code})
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} color="inherit">
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteUoMMutation.isPending}
          >
            {deleteUoMMutation.isPending
              ? t("common.loading")
              : t("common.delete")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <MuiAlert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </MuiAlert>
      </Snackbar>
    </>
  );
}

export default CurrencyExchangePage;
