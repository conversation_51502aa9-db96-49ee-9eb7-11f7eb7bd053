import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useReactiveLogo, useReactiveFavicon } from '../../hooks/useSystemPreferences';

const SystemPreferencesDebug: React.FC = () => {


  return (
    <Paper sx={{ p: 2, m: 2, backgroundColor: '#f5f5f5' }}>
   

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>Current Images:</Typography>
       
       
      </Box>
    </Paper>
  );
};

export default SystemPreferencesDebug;
