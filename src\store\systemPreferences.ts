import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Extend Window interface to include updateFavicon
declare global {
  interface Window {
    updateFavicon?: (url: string) => void;
  }
}

interface SystemPreferences {
  id?: string;
  default_time_zone: string;
  date_format: string;
  default_language: string;
  default_from_email: string;
  no_reply_email: string;
  support_contact_email: string;
  admin_contact_email: string;
  email_signature: string;
  logoUrl?: string;
  faviconUrl?: string;
}

interface SystemPreferencesState {
  preferences: Partial<SystemPreferences>;
  logoUrl: string | null;
  faviconUrl: string | null;
  setPreferences: (preferences: Partial<SystemPreferences>) => void;
  setLogoUrl: (url: string | null) => void;
  setFaviconUrl: (url: string | null) => void;
  updateFavicon: (url: string) => void;
  getLogoUrl: () => string;
  getFaviconUrl: () => string;
}

const BASE_URL = import.meta.env.VITE_ASSETS_BASE_URL || '';
const FAVICON_BASE_URL = import.meta.env.VITE_ASSETS_FAVICON_URL || '';

export const useSystemPreferencesStore = create<SystemPreferencesState>()(
  persist(
    (set, get) => ({
      preferences: {},
      logoUrl: null,
      faviconUrl: null,

      setPreferences: (preferences) => {
        set({ preferences });
        if (preferences.logoUrl) {
          set({ logoUrl: preferences.logoUrl });
        }
        if (preferences.faviconUrl) {
          set({ faviconUrl: preferences.faviconUrl });
          get().updateFavicon(preferences.faviconUrl);
        }
      },

      setLogoUrl: (url) => set({ logoUrl: url }),

      setFaviconUrl: (url) => {
        set({ faviconUrl: url });
        if (url) {
          get().updateFavicon(url);
        }
      },

      updateFavicon: (url) => {
        // Update the favicon in the document head
        const fullUrl = url.startsWith('http') ? url : `${FAVICON_BASE_URL}${url}`;

        // Use the global updateFavicon function if available
        if (typeof window !== 'undefined' && window.updateFavicon) {
          window.updateFavicon(fullUrl);
        } else {
          // Fallback method
          const existingLinks = document.querySelectorAll('link[rel*="icon"]');
          existingLinks.forEach(link => link.remove());
          const link = document.createElement('link');
          link.rel = 'icon';
          link.type = 'image/x-icon';
          link.href = fullUrl;
          document.head.appendChild(link);
        }
      },

      getLogoUrl: () => {
        const { logoUrl } = get();
        if (!logoUrl) return `${BASE_URL}logo.png`; // fallback
        return logoUrl.startsWith('http') ? logoUrl : `${BASE_URL}${logoUrl}`;
      },

      getFaviconUrl: () => {
        const { faviconUrl } = get();
        if (!faviconUrl) return `${FAVICON_BASE_URL}favicon.svg`; // fallback to default favicon
        return faviconUrl.startsWith('http') ? faviconUrl : `${FAVICON_BASE_URL}${faviconUrl}`;
      },

      // Computed properties for reactive access
      get computedLogoUrl() {
        const { logoUrl } = get();
        if (!logoUrl) return `${BASE_URL}logo.png`; // fallback
        return logoUrl.startsWith('http') ? logoUrl : `${BASE_URL}${logoUrl}`;
      },

      get computedFaviconUrl() {
        const { faviconUrl } = get();
        if (!faviconUrl) return `${FAVICON_BASE_URL}favicon.svg`; // fallback to default favicon
        return faviconUrl.startsWith('http') ? faviconUrl : `${FAVICON_BASE_URL}${faviconUrl}`;
      },
    }),
    {
      name: 'system-preferences-storage',
      partialize: (state) => ({
        preferences: state.preferences,
        logoUrl: state.logoUrl,
        faviconUrl: state.faviconUrl,
      }),
    }
  )
);
