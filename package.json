{"name": "admin-dashboard", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^5.14.15", "@mui/material": "^5.17.1", "@tanstack/react-query": "^5.76.1", "axios": "^1.5.1", "dayjs": "^1.11.13", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "install": "^0.13.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.344.0", "moment": "^2.30.1", "npm": "^11.4.0", "react": "^18.3.1", "react-currency-input-field": "^3.10.0", "react-dom": "^18.3.1", "react-hook-form": "^7.47.0", "react-i18next": "^13.5.0", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-router-dom": "^6.30.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "suneditor": "^2.47.5", "suneditor-react": "^3.6.1", "yup": "^1.6.1", "zod": "^3.22.4", "zustand": "^4.4.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/lodash": "^4.17.16", "@types/node": "^22.15.21", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5"}}