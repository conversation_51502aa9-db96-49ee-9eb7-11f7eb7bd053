export const formatStatus = (status: string): string => {
  if (!status) return "N/A";
  const formatted = status
    .toLowerCase()
    .replace(/_/g, " ")
    .replace(/^./, str => str.toUpperCase());
  return formatted;
};
export const getFilteredNavItems = (navItems: any[], role: string) => {
  return navItems
    .map((item) => {
      if (item.children) {
        const filteredChildren = item.children.filter((child: any) =>
          child.roles?.includes(role)
        );

        // ✅ If any child is accessible, return parent with filtered children
        if (filteredChildren.length) {
          return {
            ...item,
            children: filteredChildren,
          };
        }

        // ✅ If no children but parent is accessible
        return item.roles?.includes(role) ? { ...item, children: [] } : null;
      }

      // ✅ Standalone item
      return item.roles?.includes(role) ? item : null;
    })
    .filter(Boolean);
};

export const stepRoutes = [
  "/vendor-onboarding/company-details",
  "/vendor-onboarding/contact-details",
  "/vendor-onboarding/location",
  "/vendor-onboarding/payment-delivery",
  "/vendor-onboarding/certifications",
  "/vendor-onboarding/review-submit",
];

export const handleError = (error: any): string => {
  if (error.response?.data?.message) return error.response.data.message;
  if (error.message) return error.message;
  return "An unexpected error occurred";
};

export function formatColumnName(text: string): string {
  return text
    .split("_")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};