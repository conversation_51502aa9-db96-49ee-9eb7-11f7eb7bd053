// src/pages/preferences/ChangeHistoryPage.tsx
import { <PERSON>alog, DialogTitle, DialogContent, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Table, { ColumnType } from "../../components/Table";
import { useEffect, useMemo, useState} from "react";
import _ from "lodash";
import sortHandler from "../../components/Table/sortHandler";
import { getAllGlobalSystemPreferences, getSystemPreferencesHistory } from "../../services/preferences/preferences";
import { useQuery } from "@tanstack/react-query";
import { useLanguageStore } from "../../store/language";
import { formatDate } from "../../utils/dateFormats";
import { formatColumnName } from "../../utils";

const ChangeHistoryPage = () => {
const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(0);
  const [pageCount, setPageCount] = useState<number>(10);
  const [sort, setSort] = useState<any>({});

  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });

  const { data: changeHistoryData, isLoading } = useQuery({
    queryKey: ["history", page, pageCount, sort],
    queryFn: async () => {
      const response = await getSystemPreferencesHistory();
      const history = response?.data?.result || [];
      return history
    },
  });

  const totalCount = changeHistoryData?.length || 0;

  const defaultColumns: Array<ColumnType> = useMemo(
    () => [
        {
        title: t("systemPreferences.columnName"),
        key: "column_name",
        sort: true,
        searchable: true,
        filterable: true,
        render: (row) => formatColumnName(row.column_name),

      },
      {
        title: t("systemPreferences.oldValue"),
        key: "old_value",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("systemPreferences.newValue"),
        key: "new_value",
        sort: true,
        searchable: true,
        filterable: true,
      },{
        title: t("systemPreferences.changedBy"),
        key: "changed_by",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("systemPreferences.changedDate"),
        key: "changed_date",
        sort: true,
        filterable: true,
        render: (row) => {
          const dateFormat =
            preferences?.result[0]?.date_format || "YYYY-MM-DD";
          return <span>{formatDate(row.createdAt, dateFormat)}</span>;
        },
      },
     
    ],
    [t, currentLanguage, preferences]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const handleClose = () => {
    navigate(-1); // goes back to /system-preferences
  };

  return (
    <Dialog open onClose={handleClose} fullWidth maxWidth="lg">
      <DialogTitle>
        {t("systemPreferences.changeHistorySystem")}
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{ position: "absolute", right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
          <Table
           sx={{height:'auto'}}
          columns={columns}
          data={changeHistoryData || []}
          sortHandler={handleSort}
          pagination={{
            totalCount,
            pageCount,
            setPageCount,
            page,
            setPage,
          }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />
      </DialogContent>
    </Dialog>
  );
};

export default ChangeHistoryPage;
