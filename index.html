<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" id="dynamic-favicon" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <title>PMI Vendor Portal</title>
    
    <script>
      // Dynamic favicon updater
      window.updateFavicon = function(faviconUrl) {
        const link = document.getElementById('dynamic-favicon') || document.querySelector('link[rel*="icon"]');
        if (link) {
          link.href = faviconUrl;
        } else {s
          const newLink = document.createElement('link');
          newLink.id = 'dynamic-favicon';
          newLink.rel = 'icon';
          newLink.type = 'image/x-icon';
          newLink.href = faviconUrl;
          document.head.appendChild(newLink);
        }
      };
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>