// services/vendorPMIContacts.ts
import axios from "axios";
import api from "../httpClient/api";

export interface VendorPMIContact {
  id?: number;
  vendor_id: number;
  pmi_contact_id: number;
  department_ids: number[];
  updated_by: string;
  createdAt?: string;
  updatedAt?: string;
}


export interface InternalContact {
  id: string;
  name: string;
  email: string;
}

export interface Department {
  id: string;
  department_name: string;
}

export const getAllVendorPMIContacts = async () => {
  const res = await axios.get("/api/vendor-pmi-contacts");
  return res.data;
};
export const getMapContactByVendorId = async (id: number) => {
  const response = await api.get(`/user/pmi-contacts-vendor-mapping/vendor/${id}`);
  return response.data;
};

export const createVendorPMIContact = async (data: VendorPMIContact) => {
  const res = await api.post("/user/create-pmi-contacts-vendor-mapping", data);
  return res.data;
};
export const updateVendorPMIContact = async (payload: {
  id: number;
  department_id: number;
  pmi_contact_ids: number[];
  updated_by: string;
}) => {
  return api.post(`/user/update-pmi-contacts-vendor-mapping`, payload);
};


export const deleteVendorPMIContact = async (id: number) => {
  const res = await api.delete(`/user/delete-pmi-contacts-vendor-mapping/${id}`);
  return res.data;
};
export const getInternalContacts = async (): Promise<InternalContact[]> => {
  const res = await axios.get("/api/internal-contacts");
  return res.data.result;
};

export const getDepartments = async (): Promise<Department[]> => {
  const res = await api.get("/user/departments");
  return res.data.result;
};