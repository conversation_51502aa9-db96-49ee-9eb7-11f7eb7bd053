import React, { ReactNode, useState, useEffect } from 'react';
import { Box, Toolbar } from '@mui/material';
import AppBar from './AppBar';
import SideNav from './SideNav';
import { useSessionTimeout } from '../../utils/sessionTimeout';

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [persistExpanded, setPersistExpanded] = useState(false);
  const [collapseChildren, setCollapseChildren] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
    setCollapseChildren(false);
  };

  const handleMouseLeave = () => {
    if (!persistExpanded) {
      setIsHovered(false);
      setCollapseChildren(true);
    }
  };

  const handleToggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
    setPersistExpanded(!isDrawerOpen);
    setCollapseChildren(isDrawerOpen); // collapse when closing manually
  };

  useSessionTimeout();

  const collapsedWidth = 64;
  const expandedWidth = 240;
  const drawerWidth = isDrawerOpen || isHovered || persistExpanded ? expandedWidth : collapsedWidth;

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <AppBar open={isDrawerOpen || isHovered || persistExpanded} toggleDrawer={handleToggleDrawer} drawerWidth={drawerWidth} />

      <Box
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        sx={{ height: '100vh' }}
      >
        <SideNav
          open={isDrawerOpen || isHovered || persistExpanded}
          drawerWidth={drawerWidth}
          handleDrawerToggle={() => setMobileOpen(!mobileOpen)}
          mobileOpen={mobileOpen}
          collapseChildren={collapseChildren}
        />
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          overflowX: 'hidden',
          bgcolor: (theme) =>
            theme.palette.mode === 'light' ? 'grey.100' : 'grey.900',
          minHeight: '100vh',
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

export default MainLayout;