// import { Add } from "@mui/icons-material";
import { Box, IconButton, Typography } from "@mui/material";
import { useEffect, useState } from "react";
// import { useNavigate } from "react-router-dom";
import Table from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import { useQuery } from "@tanstack/react-query";
import _ from "lodash";
import { getAllmailTemplates } from "../../services/notification/notification";
import { useAuthStore } from "../../store/auth";
import VisibilityIcon from "@mui/icons-material/Visibility";
import EditIcon from "@mui/icons-material/Edit";
import Loader from "../../utils/Loader";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
function EmailTemplates() {
  // const navigate = useNavigate();
  const [page, setPage] = useState<number>(0);
  const [pageCount, setPageCount] = useState<number>(10);
  const [sort, setSort] = useState<any>({});
  const { user } = useAuthStore();
  const navigate = useNavigate();

  const { t, i18n } = useTranslation();

  const getColumns = () => [
    {
      title: t("notification.notificationTitle"),
      key: "notification_title",
      sort: true,
      searchable: true,
      filterable: true,
      width: "200px",
    },
    {
      title: t("notification.subject"),
      key: "subject",
      sort: true,
      searchable: true,
      filterable: true,
      width: "200px",
    },
    {
      title: t("notification.messageBody"),
      key: "message_body",
      searchable: true,
      filterable: false,
      width: "300px",
      render: (row: any) => {
        const plainText = row.message_body.replace(/<[^>]+>/g, "");
        return (
          <div
            style={{
              whiteSpace: "nowrap",
              maxWidth: "300px",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
            title={plainText}
          >
            {plainText}
          </div>
        );
      },
    },
    {
      title: t("notification.actions"),
      key: "actions",
      searchable: false,
      filterable: false,
      width: "120px",
      render: (row: any) => (
        <Box display="flex" gap={1}>
          <IconButton
            size="small"
            onClick={() => navigate(`/notification-template/preview/${row.id}`)}
            aria-label="Preview"
          >
            <VisibilityIcon />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => navigate(`/notification-template/edit/${row.id}`)}
            aria-label="Edit"
          >
            <EditIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  useEffect(() => {
    setColumns(getColumns());
  }, [i18n.language]);

  const [columns, setColumns] = useState(getColumns());
  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };
  const { data: existingPreferences, isLoading } = useQuery({
    queryKey: ["getAllmailTemplates"],
    queryFn: async () => {
      const response = await getAllmailTemplates();
      return response.result;
    },
  });

  const totalCount = existingPreferences?.length || 0;
  if (isLoading) return <Loader value={"Loading..."} />;
  if (user?.role === "business") {
    return (
      <>
        <Box px={1} py={1}>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            mt={2}
          >
            <Typography variant="subtitle1" color="primary">
              {t("notification.title")}
            </Typography>
          </Box>

          {existingPreferences && existingPreferences.length > 0 ? (
            <Table
              sx={{height:'auto'}}
              pagination={{
                totalCount,
                pageCount,
                setPageCount,
                page,
                setPage,
              }}
              data={existingPreferences}
              columns={columns}
              loading={isLoading} // Correct usage
              sortHandler={handleSort}
              enableSearch={true}
              enableFilter={true}
              searchPlaceholder={t("table.searchPlaceholder")}
            />
          ) : (
            <Box textAlign="center" mt={20}>
              <Typography variant="subtitle1" color="rgba(0,0,0,0.5)">
                No Data present
              </Typography>
            </Box>
          )}
        </Box>
      </>
    );
  }

  if (user?.role === "vendor") {
    return (
      <Box textAlign="center" mt={10}>
        <Typography variant="h4" gutterBottom>
          Permission Denied
        </Typography>
      </Box>
    );
  }

  return null; // fallback for undefined or other roles
}

export default EmailTemplates;
