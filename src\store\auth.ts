import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  AuthState,
  LoginFormValues,
  ForgotPasswordFormValues,
} from '../types';
import { login, forgotPassword } from '../services/auth/authService';

interface AuthStore extends AuthState {
  setUser: (user: AuthState['user']) => void;
  setToken: (token: string | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setHasCompleted2FA: (status: boolean) => void;

  handleLogin: (values: LoginFormValues) => Promise<boolean>;
  handleForgotPassword: (values: ForgotPasswordFormValues) => Promise<boolean>;
  logout: () => void;
}

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  temporaryToken: null,
  hasCompleted2FA: false,
};

export type LoginResult = {
  success: boolean;
  status: number;
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set) => ({
      ...initialState,

      setUser: (user) => set({ user, isAuthenticated: !!user }),
      setToken: (token) => set({ token }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      setHasCompleted2FA: (status) => set({ hasCompleted2FA: status }),

      handleLogin: async (values) => {
        try {
          set({ loading: true, error: null });
          const response: any = await login(values);

          if (response.token && response.user) {
            set({
              token: response.token,
              user: response.user,
              isAuthenticated: true,
              loading: false,
              hasCompleted2FA: false,
            });
            return true;
          } else {
            set({ loading: false });
            return false;
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'An error occurred during login',
            loading: false,
          });
          return false;
        }
      },

      handleForgotPassword: async (values) => {
        try {
          set({ loading: true, error: null });
          await forgotPassword(values.email);
          set({ loading: false });
          return true;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'An error occurred',
            loading: false,
          });
          return false;
        }
      },

      logout: () => {
        set(initialState);
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        hasCompleted2FA: state.hasCompleted2FA,
      }),
    }
  )
);
