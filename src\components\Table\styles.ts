import { styled } from "@mui/material/styles";
import { Table, TableContainer, Box } from "@mui/material";

// Container with rounded corners and shadow
export const StyledTableContainer = styled(TableContainer)(({  }) => ({
  borderRadius: '12px',
  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
  overflowX: 'auto',
  overflowY: 'auto',
  backgroundColor: '#fff',
  width: '100%',
  height: '70vh',
}));

// Table styling
export const StyledTable = styled(Table)(({ theme }) => ({
  borderCollapse: 'separate',
  borderSpacing: '0 0',
  width: '100%',

  '& thead th': {
    // background: 'linear-gradient(to right, #0f4c75, #3282b8)',
    background:'#004B8D',
    color: '#fff',
    fontWeight: 600,
    padding: theme.spacing(1.5),
    textAlign: 'left',
    border: 'none',
    fontSize: '14px',
  },

  // '& tbody tr': {
  //   backgroundColor: '#f0f8ff',
  //   borderRadius: '8px',
  //   transition: 'background-color 0.3s ease',
  //   cursor: 'pointer',
  // },
  '& tbody tr:nth-of-type(odd)': {
      backgroundColor: '#f0f8ff', // light blue for odd rows
    },
    '& tbody tr:nth-of-type(even)': {
      backgroundColor: '#ffffff', // white for even rows
    },
    '& tbody tr:hover': {
      backgroundColor: '#e0e0e0', // light grey on hover
    },

  '& tbody td': {
    padding: theme.spacing(1.5),
    borderTop: '1px solid #f0f0f0',
    borderBottom: '1px solid #f0f0f0',
    fontSize: '14px',
  },

  '& td:first-of-type, & th:first-of-type': {
    borderTopLeftRadius: '0px',
    borderBottomLeftRadius: '0px',
  },

  '& td:last-of-type, & th:last-of-type': {
    borderTopRightRadius: '0px',
    borderBottomRightRadius: '0px',
  },
}));

// Loader box
export const StyledTableLoader = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '200px',
}));

export const StyledTaskBox = styled(Box)({
  borderRadius: "10px",
  border: "1px solid #0000001A",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  "& header": {
    background: "rgba(0, 0, 0, 0.06)",
    padding: "10px",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  "& footer": {
    borderTop: "1px solid #0000001A",
    display: "flex",
    justifyContent: "space-between",
    padding: "10px",
    alignItems: "center",
  },
  "& main": {
    padding: "10px",
    flex: 1,
    overflowY: "auto",
  },
});