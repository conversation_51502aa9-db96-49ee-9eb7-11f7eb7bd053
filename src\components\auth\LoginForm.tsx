import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, Link } from 'react-router-dom';
import {
  TextField,
  Button,
  FormControlLabel,
  Checkbox,
  Box,
  Typography,
  CircularProgress,
  InputAdornment,
  IconButton,
  Alert,
  Snackbar,
  <PERSON>ert as Mui<PERSON><PERSON>t,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { login } from '../../services/auth/authService';
import { useLanguageStore } from '../../store/language';

const LoginForm: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const navigate = useNavigate();

  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [formValues, setFormValues] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const [formErrors, setFormErrors] = useState({
    email: '',
    password: '',
  });

  const [generalError, setGeneralError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Update error messages when language changes
  useEffect(() => {
    // Re-translate existing error messages when language changes
    if (formErrors.email) {
      setFormErrors(prev => ({
        ...prev,
        email: prev.email ? t('forms.validation.email') : ''
      }));
    }
    if (formErrors.password) {
      setFormErrors(prev => ({
        ...prev,
        password: prev.password ? t('forms.validation.required') : ''
      }));
    }
  }, [currentLanguage, i18n.language, t]);

const { mutate, isPending } = useMutation({
  mutationFn: login,
  onSuccess: (res: any) => {
    if (res?.data?.status === 200 || res?.status === 200) {
      toast.success(t('auth.loginSuccess'));
      localStorage.setItem('twoFactorEmail', formValues.email);
      localStorage.removeItem('isVendorLogin'); // Clear vendor flag for regular login
      const userData = res?.data?.user || { email: formValues.email };
      const tokenData = res?.data?.token || `partial-auth-${Date.now()}`;
      localStorage.setItem('auth-storage', JSON.stringify({
        state: {
          token: tokenData,
          user: userData,
          isAuthenticated: false,
          hasCompleted2FA: false
        },
        version: 0
      }));

      setSnackbarOpen(true);
      setTimeout(() => {
        navigate('/two-factor', {
          state: {
            email: formValues.email,
            from: 'login'
          },
          replace: true
        });
      }, 1500);
    } else {
      console.log(res?.data,"...................")
      const errorMessage = res?.data?.message || res?.message || t('auth.invalidCredentials');
      setGeneralError(errorMessage);
      toast.error(errorMessage);
    }
  },
  onError: (err: any) => {
    console.log(err,"EEEEEEEEEEEEEEEEEE")
    console.log(err,"Error message")
    let errorMessage = '';

    if (err.response?.data?.statusCode === 401 || err.response?.status === 401) {
      errorMessage = t('auth.invalidCredentials');
    } else if (err.response?.data?.statusCode === 403 || err.response?.status === 403) {
      if (err.response?.data?.message?.includes('locked')) {
        errorMessage = t('auth.accountLocked');
      } else if (err.response?.data?.message?.includes('disabled')) {
        errorMessage = t('auth.accountDisabled');
      } else {
        errorMessage = err.response?.data?.message || t('auth.accessDenied');
      }
    } else if (err.response?.data?.message) {
      errorMessage = err.response.data.message;
    } else if (err.message) {
      errorMessage = err.message;
    } else {
      errorMessage = t('auth.somethingWentWrong');
    }

    setGeneralError(errorMessage);

    toast.error(errorMessage);
  },
});


  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    setFormValues({
      ...formValues,
      [name]: name === 'rememberMe' ? checked : value,
    });

    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formValues.email) {
      newErrors.email = t('forms.validation.required');
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formValues.email)) {
      newErrors.email = t('forms.validation.email');
      valid = false;
    } else {
      newErrors.email = '';
    }

    if (!formValues.password) {
      newErrors.password = t('forms.validation.required');
      valid = false;
    } else {
      newErrors.password = '';
    }

    setFormErrors(newErrors);
    return valid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setGeneralError('');
    if (!validateForm()) return;
    mutate(formValues);
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <>
      <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
        {/* Display general error message */}
        {generalError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {generalError}
          </Alert>
        )}

        {formErrors.email && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {formErrors.email}
          </Alert>
        )}
        {formErrors.password && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {formErrors.password}
          </Alert>
        )}

        <TextField
          margin="normal"
          required
          fullWidth
          id="email"
          label={t('auth.email')}
          name="email"
          autoComplete="email"
          autoFocus
          value={formValues.email}
          onChange={handleChange}
          error={!!formErrors.email}
          helperText={formErrors.email}
          disabled={isPending}
          placeholder={t('forms.placeholders.enterEmail')}
        />

        <TextField
          margin="normal"
          required
          fullWidth
          name="password"
          label={t('auth.password')}
          type={showPassword ? 'text' : 'password'}
          id="password"
          autoComplete="current-password"
          value={formValues.password}
          onChange={handleChange}
          error={!!formErrors.password}
          helperText={formErrors.password}
          disabled={isPending}
          placeholder={t('forms.placeholders.enterPassword')}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={toggleShowPassword} edge="end">
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 1,
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                name="rememberMe"
                color="primary"
                checked={formValues.rememberMe}
                onChange={handleChange}
                disabled={isPending}
              />
            }
            label={t('auth.rememberMe')}
          />

          <Typography variant="body2">
            <Link to="/forgot-password" style={{ color: 'inherit', textDecoration: 'none' }}>
              {t('auth.forgotPassword')}
            </Link>
          </Typography>
        </Box>

        <Button
          type="submit"
          fullWidth
          variant="contained"
          disabled={isPending}
          sx={{ mt: 3, mb: 2, py: 1.5 }}
        >
          {isPending ? <CircularProgress size={24} color="inherit" /> : t('auth.login')}
        </Button>
      </Box>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <MuiAlert onClose={() => setSnackbarOpen(false)} severity="info" sx={{ width: '100%' }}>
          {t('auth.verificationCodeSent')}
        </MuiAlert>
      </Snackbar>
    </>
  );
};

export default LoginForm;
