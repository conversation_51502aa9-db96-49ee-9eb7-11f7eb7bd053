import api from "../httpClient/api";

export interface Vendor {
  id?: number;
  vendor_name: string;
  vendor_email: string;
  vendor_phone?: string;
  vendor_address?: string;
  vendor_city?: string;
  vendor_state?: string;
  vendor_country?: string;
  vendor_zip_code?: string;
  vendor_website?: string;
  vendor_description?: string;
  vendor_type?: string;
  vendor_status?: 'active' | 'inactive';
  contact_person?: string;
  tax_id?: string;
  business_license?: string;
  service_category?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Get all vendors
export const getAllVendors = async () => {
  console.log("getAllVendors service: Starting request...");
  const response = await api.get('/vendors');
  console.log("getAllVendors service: Request successful:", response);
  return response.data;
};

// Get vendor by ID
export const getVendorById = async (id: number) => {
  const response = await api.get(`/vendors/${id}`);
  return response.data;
};

// Create new vendor
export const createVendor = async (vendorData: Omit<Vendor, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const response = await api.post('/vendors', vendorData);
    return response.data;
  } catch (error) {
    console.error("Error creating vendor:", error);
    throw error;
  }
};

// Update vendor
export const updateVendor = async (id: number, vendorData: Partial<Vendor>) => {
  try {
    const response = await api.put(`/vendors/${id}`, vendorData);
    return response.data;
  } catch (error) {
    console.error("Error updating vendor:", error);
    throw error;
  }
};

// Delete vendor
export const deleteVendor = async (id: number) => {
  try {
    const response = await api.delete(`/vendors/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting vendor:", error);
    throw error;
  }
};

// Search vendors
export const searchVendors = async (searchTerm: string) => {
  const response = await api.get(`/vendors/search?q=${encodeURIComponent(searchTerm)}`);
  return response.data;
};

// Get vendors by status
export const getVendorsByStatus = async (status: 'active' | 'inactive') => {
  const response = await api.get(`/vendors?status=${status}`);
  return response.data;
};

// Get vendors by type
export const getVendorsByType = async (type: string) => {
  const response = await api.get(`/vendors?type=${type}`);
  return response.data;
};
