import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON>,
  Typography,
  IconButton,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Chip,
  Autocomplete,
} from "@mui/material";
import { Edit } from "@mui/icons-material";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import _ from "lodash";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import { useNavigate } from "react-router-dom";
import useQueryParams from "../../hooks/useQueryParams";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import Loader from "../../utils/Loader";
import { getAllGlobalSystemPreferences } from "../../services/preferences/preferences";
import {
  ViewallUsers,
  ViewallVendors,
  assignUserRole,
  unAssignRole,
} from "../../services/auth/authService";
import { getAllRoles } from "../../services/roles/roles";
import { StyledProfileNav, StyledProfileNavItem } from "../roles/styles";
import api from "../../services/httpClient/api";

const userRolesTabs = [
  {
    titleKey: "userRoles.businessUsers",
    path: "business-users",
    userType: "business" as const,
  },
  {
    titleKey: "userRoles.vendorUsers",
    path: "vendor-users",
    userType: "vendor" as const,
  },
];

const AssignUserRoles = () => {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { queryParams } = useQueryParams();
  const active: any = queryParams.tab || userRolesTabs[0].path;

  const currentTab =
    userRolesTabs.find((tab) => tab.path === active) || userRolesTabs[0];

  const [page, setPage] = useState(0); // Page index, starting from 0
  const [rowsPerPage, setRowsPerPage] = useState(10); // Number of rows per page
  const [pageCount, setPageCount] = useState(10);
  const [sort, setSort] = useState<any>({});
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error",
  });

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [csvDialogOpen, setCsvDialogOpen] = useState(false);
  const [csvMessage, setCsvMessage] = useState("");
  const [csvSeverity, setCsvSeverity] = useState<"success" | "error">(
    "success"
  );
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [isUploadingCSV, setIsUploadingCSV] = useState(false);
  const [csvResponse, setCsvResponse] = useState<any>(null);
  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });

  const { data: usersData = [], isLoading } = useQuery({
    queryKey: ["ViewallUsers", currentTab.userType, page, rowsPerPage],
    queryFn: async () => {
      const response =
        currentTab.userType === "business"
          ? await ViewallUsers()
          : await ViewallVendors();

      return response.result || [];
    },
  });

  const { data: rolesData = [] } = useQuery({
    queryKey: ["roles", currentTab.userType],
    queryFn: async () => {
      const response = await getAllRoles();
      const allRoles = response.result || [];
      return allRoles.filter(
        (role: any) => role.userType === currentTab.userType
      );
    },
  });

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const handleAssignRole = (user: any) => {
    setSelectedUser(user);
    const currentRoleIds = user.roles?.map((role: any) => role.id) || [];
    setSelectedRoleIds(currentRoleIds);
    setEditDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!selectedUser) return;

    setIsSubmitting(true);

    const initialRoleIds =
      selectedUser.roles?.map((role: any) => role.id) || [];
    const rolesToAdd = selectedRoleIds.filter(
      (id) => !initialRoleIds.includes(id)
    );
    const rolesToRemove: any = initialRoleIds.filter(
      (id: any) => !selectedRoleIds.includes(id)
    );

    try {
      // Call assign API for new roles
      if (rolesToAdd.length > 0) {
        await assignUserRole({
          userId: selectedUser.id,
          data: { roleIds: rolesToAdd, userType: currentTab.userType },
        });
      }

      // Call unassign API for removed roles
      if (rolesToRemove.length > 0) {
        await unAssignRole({
          userId: selectedUser.id,
          data: { roleIds: rolesToRemove, userType: currentTab.userType },
        });
      }

      queryClient.invalidateQueries({ queryKey: ["ViewallUsers"] });
      setSnackbar({
        open: true,
        message: t("userRoles.rolesUpdated"),
        severity: "success",
      });
      handleDialogClose();
    } catch (error: any) {
      const msg = error?.response?.data?.message || t("common.unknownError");
      setSnackbar({ open: true, message: msg, severity: "error" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDialogClose = () => {
    setEditDialogOpen(false);
    setSelectedUser(null);
    setSelectedRoleIds([]);
    setIsSubmitting(false);
  };

  const handleCSVChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === "text/csv") {
      setCsvFile(file);
    } else {
      setCsvMessage("Please upload a valid CSV file.");
      setCsvSeverity("error");
      setCsvDialogOpen(true);
    }
  };

  const handleUploadCSV = async () => {
    if (!csvFile) {
      setCsvMessage("No CSV file selected.");
      setCsvSeverity("error");
      setCsvDialogOpen(true);
      return;
    }

    setIsUploadingCSV(true);
    try {
      const formData = new FormData();
      formData.append("file", csvFile);
      formData.append("userType", currentTab.userType);

      const response = await api.post("/user/upload-roles-map", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      const isSuccess = response.data?.success === true;

      // Save full response for error display
      setCsvResponse(response.data);

      setCsvSeverity(isSuccess ? "success" : "error");
      setCsvMessage(
        response.data?.message ||
          (isSuccess ? "CSV uploaded successfully." : "Upload failed.")
      );
      setCsvDialogOpen(true);
      queryClient.invalidateQueries({ queryKey: ["ViewallUsers"] });
    } catch (error: any) {
      const errMsg =
        error?.response?.data?.message || "Failed to upload CSV file.";
      setCsvMessage(errMsg);
      setCsvSeverity("error");
      setCsvDialogOpen(true);
    } finally {
      setIsUploadingCSV(false);
      setCsvFile(null);
    }
  };

  const defaultColumns: ColumnType[] = useMemo(
    () => [
      {
        title: t("userRoles.users"),
        key: "first_name",
        sort: true,
        searchable: true,
        filterable: true,
        render: (row) => (
          <span>
            {row?.first_name} {row?.last_name}
            <br />
            {row?.email}
          </span>
        ),
      },
      {
        title: t("userRoles.roles"),
        key: "role_name",
        dataIndex: "roles",
        sort: false,
        width: 250,
        render: (row: any) => (
          <span
            style={{
              wordWrap: "break-word",
              whiteSpace: "normal",
              display: "block",
            }}
          >
            {row.roles?.map((r: any) => r.role_name).join(", ") || "-"}
          </span>
        ),
      },
      {
        title: t("userRoles.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        render: (row: any) => (
          <Box display="flex" gap={1}>
            <IconButton size="small" onClick={() => handleAssignRole(row)}>
              <Edit />
            </IconButton>
          </Box>
        ),
      },
    ],
    [t, currentLanguage, preferences]
  );
  const totalLength = usersData.length;
  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Box sx={{ position: "relative", bgcolor: "white", zIndex: 2 }}>
        <StyledProfileNav>
          {userRolesTabs.map((item, index) => (
            <StyledProfileNavItem
              key={index}
              onClick={() => navigate(`?tab=${item.path}`)}
              active={active === item.path ? 1 : 0}
            >
              {t(item.titleKey)}
            </StyledProfileNavItem>
          ))}
        </StyledProfileNav>
      </Box>

      <Box p={3}>
        <Typography variant="h6" color="primary" gutterBottom>
          {t("userRoles.title")} - {t(currentTab.titleKey)}
        </Typography>

        <Box display="flex" justifyContent="flex-end" mb={2} gap={2}>
          <Button variant="contained" component="label">
            {t("userRoles.uploadCSV")}
            <input
              type="file"
              accept=".csv"
              hidden
              onChange={handleCSVChange}
            />
          </Button>
          <Button
            variant="outlined"
            onClick={handleUploadCSV}
            disabled={!csvFile || isUploadingCSV}
          >
            {isUploadingCSV ? t("common.uploading") : t("userRoles.upload")}
          </Button>
        </Box>

        <Typography
          variant="body2"
          color="error"
          sx={{ fontWeight: 500, fontSize: 12 }}
        >
          *Mandatory columns in the CSV - <strong>email_id</strong>,{" "}
          <strong>roles</strong>. Roles names should be valid & can be comma
          separated.
        </Typography>

        <Dialog open={csvDialogOpen} onClose={() => setCsvDialogOpen(false)}>
          <DialogTitle>
            {csvSeverity === "success" ? "Success" : "Error"}
          </DialogTitle>
          <DialogContent>
            {csvMessage && <Typography mb={2}>{csvMessage}</Typography>}

            {csvResponse?.failed_rows?.length > 0 ||
            csvResponse?.failed_emails?.length > 0 ||
            csvResponse?.failed_roles?.length > 0 ? (
              <Box sx={{ overflowX: "auto" }}>
                <Typography variant="subtitle1" fontWeight={600} mb={1}>
        Failed Rows:
      </Typography>
                <table style={{ borderCollapse: "collapse", width: "100%" }}>
                  <thead>
                    <tr
                      style={{ backgroundColor: "#f0f0f0", textAlign: "left" }}
                    >
                      <th style={{ border: "1px solid #ccc", padding: "8px" }}>
                        Email ID
                      </th>
                      <th style={{ border: "1px solid #ccc", padding: "8px" }}>
                        roles
                      </th>
                      <th style={{ border: "1px solid #ccc", padding: "8px" }}>
                        errors
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {csvResponse?.failed_rows?.map(
                      (rowIndex: number, idx: number) => {
                        const email = csvResponse?.failed_emails?.[idx] || "-";
                        const roles = "-"; // If you want, you can enhance this by storing parsed CSV rows
                        const errorMsg = csvResponse?.failed_emails?.includes(
                          email
                        )
                          ? "email not found"
                          : csvResponse?.failed_roles?.length > 0
                          ? "role not found"
                          : "unknown error";

                        return (
                          <tr key={rowIndex}>
                            <td
                              style={{
                                border: "1px solid #ccc",
                                padding: "8px",
                              }}
                            >
                              {email}
                            </td>
                            <td
                              style={{
                                border: "1px solid #ccc",
                                padding: "8px",
                              }}
                            >
                              {roles}
                            </td>
                            <td
                              style={{
                                border: "1px solid #ccc",
                                padding: "8px",
                              }}
                            >
                              {errorMsg}
                            </td>
                          </tr>
                        );
                      }
                    )}
                  </tbody>
                </table>
              </Box>
            ) : (
              <Typography>No detailed errors found.</Typography>
            )}
          </DialogContent>

          <DialogActions>
            <Button onClick={() => setCsvDialogOpen(false)} autoFocus>
              {t("common.close")}
            </Button>
          </DialogActions>
        </Dialog>

        <Table
          sx={{ height: "auto" }}
          data={usersData}
          columns={columns}
          pagination={{
            totalCount: totalLength,

            pageCount: rowsPerPage,
            setPageCount: setRowsPerPage,
            page,
            setPage,
          }}
          sortHandler={handleSort}
          loading={isLoading}
          enableSearch
          enableFilter
          searchPlaceholder={t("table.searchPlaceholder")}
        />

        <Snackbar
          open={snackbar.open}
          autoHideDuration={4000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            severity={snackbar.severity}
            onClose={() => setSnackbar({ ...snackbar, open: false })}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>

        {/* Edit Dialog with Autocomplete for roles */}
        <Dialog
          open={editDialogOpen}
          onClose={handleDialogClose}
          fullWidth
          maxWidth="sm"
        >
          <DialogTitle>
            {t("userRoles.editUserRoles")} - {selectedUser?.first_name}{" "}
            {selectedUser?.last_name}
          </DialogTitle>
          <DialogContent dividers>
            <TextField
              label={t("userRoles.userName")}
              value={`${selectedUser?.first_name || ""} ${
                selectedUser?.last_name || ""
              }`}
              fullWidth
              margin="normal"
              disabled
            />
            <Autocomplete
              multiple
              disableCloseOnSelect
              options={rolesData}
              getOptionLabel={(option: any) => option.role_name || ""}
              value={rolesData.filter((role: any) =>
                selectedRoleIds.includes(role.id)
              )}
              onChange={(event, newValue) => {
                const newIds = newValue.map((r: any) => r.id);
                setSelectedRoleIds(newIds);
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t("userRoles.selectRoles")}
                  placeholder="Select roles"
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    key={option.id}
                    label={option.role_name}
                    {...getTagProps({ index })}
                  />
                ))
              }
              isOptionEqualToValue={(option, value) => option.id === value.id}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose}>{t("common.cancel")}</Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              variant="contained"
            >
              {isSubmitting ? t("common.saving") : t("common.save")}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </>
  );
};

export default AssignUserRoles;
