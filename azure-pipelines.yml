# # Node.js with React
# # Build a Node.js project that uses React.
# # Add steps that analyze code, save build artifacts, deploy, and more:
# # https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  branches:
    include:
      - main  # Automatically trigger deployment when code is pushed to the 'main' branch

pr:
  branches:
    include:
      - main  # Optional: Trigger on PR creation to the 'main' branch

pool:
  vmImage: 'ubuntu-latest'

variables:
  FRONTEND_STORAGE_ACCOUNT_DEV: 'storageaccountvms01'
  FRONTEND_CDN_PROFILE_DEV: 'ClassicCDN-VMS'
  FRONTEND_CDN_ENDPOINT_DEV: 'vms'
  RESOURCE_GROUP_DEV: 'VMS_Portal'
  
  FRONTEND_STORAGE_ACCOUNT_STAGE: 'storageaccountvmsstg'
  FRONTEND_CDN_PROFILE_STAGE: 'vms-stage'
  FRONTEND_CDN_ENDPOINT_STAGE: 'vms-stg'
  RESOURCE_GROUP_STAGE: 'VMS_Portal_Staging'

  # Environment-specific configuration files
  ENV_FILE_DEV: 'dev.env'    # Dev environment config file
  ENV_FILE_STAGE: 'stage.env'  # Stage environment config file

stages:
  - stage: Dev
    displayName: Deploy to Dev Environment
    jobs:
      - job: DeployFrontendDev
        displayName: Deploy Frontend to Dev
        condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))  # Deploy to dev when pushed to 'main'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '20.x'
            displayName: 'Install Node.js'

        # Set the environment file for Dev
          - script: |
              echo "Using environment file: $(ENV_FILE_DEV)"
              cp $(ENV_FILE_DEV) .env
            displayName: 'Set Environment File for Dev'

          - script: |
              cp $(ENV_FILE_DEV) .env
              cat .env
              npm install
              npm run build
              ls -la
            displayName: 'npm install and build'



          - task: AzureCLI@2
            inputs:
              azureSubscription: 'frontendconnection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az storage blob upload-batch \
                  --account-name $(FRONTEND_STORAGE_ACCOUNT_DEV) \
                  --destination \$web \
                  --source ./dist \
                  --overwrite
            displayName: 'Upload to Azure Blob Storage (Dev)'

          - task: AzureCLI@2
            inputs:
              azureSubscription: 'frontendconnection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az cdn endpoint purge \
                  --resource-group $(RESOURCE_GROUP_DEV) \
                  --profile-name $(FRONTEND_CDN_PROFILE_DEV) \
                  --name $(FRONTEND_CDN_ENDPOINT_DEV) \
                  --content-paths '/*'
            displayName: 'Purge Azure CDN Endpoint (Dev)'

  - stage: Stage
    displayName: Deploy to Stage Environment
    jobs:
      - job: DeployFrontendStage
        displayName: Deploy Frontend to Stage
        condition: and(succeeded(), eq(variables['Build.Reason'], 'Manual'))  # Deploy to stage when triggered manually
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '20.x'
            displayName: 'Install Node.js'
        
        # Set the environment file for Stage
          - script: |
              echo "Using environment file: $(ENV_FILE_STAGE)"
              cp $(ENV_FILE_STAGE) .env
            displayName: 'Set Environment File for Stage'

          - script: |
              cp $(ENV_FILE_STAGE) .env
              cat .env
              npm install
              npm run build
              ls -la
            displayName: 'npm install and build'

          
          - task: AzureCLI@2
            inputs:
              azureSubscription: 'frontendconnection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az storage blob upload-batch \
                  --account-name $(FRONTEND_STORAGE_ACCOUNT_STAGE) \
                  --destination \$web \
                  --source ./dist \
                  --overwrite
            displayName: 'Upload to Azure Blob Storage (Stage)'

          - task: AzureCLI@2
            inputs:
              azureSubscription: 'frontendconnection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az cdn endpoint purge \
                  --resource-group $(RESOURCE_GROUP_STAGE) \
                  --profile-name $(FRONTEND_CDN_PROFILE_STAGE) \
                  --name $(FRONTEND_CDN_ENDPOINT_STAGE) \
                  --content-paths '/*'
            displayName: 'Purge Azure CDN Endpoint (Stage)'


# trigger:
# - main

# pool:
#   vmImage: ubuntu-latest

# steps:
# - task: NodeTool@0
#   inputs:
#     versionSpec: '20.x'
#   displayName: 'Install Node.js'

# - script: |
#     npm install
#     npm run build
#     ls -la
#   displayName: 'npm install and build'

# - task: AzureCLI@2
#   inputs:
#     azureSubscription: 'frontendconnection'
#     scriptType: 'bash'
#     scriptLocation: 'inlineScript'
#     inlineScript: |
#       az storage blob upload-batch \
#         --account-name storageaccountvms01 \
#         --destination \$web \
#         --source ./dist \
#         --overwrite
#   displayName: 'Upload to Azure Blob Storage'
  
# - task: AzureCLI@2
#   inputs:
#     azureSubscription: 'frontendconnection'
#     scriptType: 'bash'
#     scriptLocation: 'inlineScript'
#     inlineScript: |
#       az cdn endpoint purge \
#         --resource-group VMS_Portal \
#         --profile-name ClassicCDN-VMS \
#         --name vms \
#         --content-paths '/*'
#   displayName: 'Purge Azure CDN Endpoint'


# trigger:
#   branches:
#     include:
#       - main  # Automatically trigger deployment when code is pushed to the 'main' branch

# pr:
#   branches:
#     include:
#       - main  # Optional: Trigger on PR creation to the 'main' branch

# pool:
#   vmImage: 'ubuntu-latest'

# variables:
#   FRONTEND_STORAGE_ACCOUNT_DEV: 'storageaccountvms01'
#   FRONTEND_CDN_PROFILE_DEV: 'ClassicCDN-VMS'
#   FRONTEND_CDN_ENDPOINT_DEV: 'vms'
#   RESOURCE_GROUP_DEV: 'VMS_Portal'

#   FRONTEND_STORAGE_ACCOUNT_STAGE: 'storageaccountvmsstg'
#   FRONTEND_CDN_PROFILE_STAGE: 'vms-stage'
#   FRONTEND_CDN_ENDPOINT_STAGE: 'vms-stg'
#   RESOURCE_GROUP_STAGE: 'VMS_Portal_Staging'

# stages:
#   - stage: Dev
#     displayName: Deploy to Dev Environment
#     jobs:
#       - job: DeployFrontendDev
#         displayName: Deploy Frontend to Dev
#         condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))  # Deploy to dev when pushed to 'main'
#         steps:
#           - task: NodeTool@0
#             inputs:
#               versionSpec: '20.x'
#             displayName: 'Install Node.js'

#           - script: |
#               npm install
#               npm run build
#               ls -la
#             displayName: 'npm install and build'

#           - task: AzureCLI@2
#             inputs:
#               azureSubscription: 'frontendconnection'
#               scriptType: 'bash'
#               scriptLocation: 'inlineScript'
#               inlineScript: |
#                 az storage blob upload-batch \
#                   --account-name $(FRONTEND_STORAGE_ACCOUNT_DEV) \
#                   --destination \$web \
#                   --source ./dist \
#                   --overwrite
#             displayName: 'Upload to Azure Blob Storage (Dev)'

#           - task: AzureCLI@2
#             inputs:
#               azureSubscription: 'frontendconnection'
#               scriptType: 'bash'
#               scriptLocation: 'inlineScript'
#               inlineScript: |
#                 az cdn endpoint purge \
#                   --resource-group $(RESOURCE_GROUP_DEV) \
#                   --profile-name $(FRONTEND_CDN_PROFILE_DEV) \
#                   --name $(FRONTEND_CDN_ENDPOINT_DEV) \
#                   --content-paths '/*'
#             displayName: 'Purge Azure CDN Endpoint (Dev)'

#   - stage: Stage
#     displayName: Deploy to Stage Environment
#     jobs:
#       - job: DeployFrontendStage
#         displayName: Deploy Frontend to Stage
#         condition: and(succeeded(), eq(variables['Build.Reason'], 'Manual'))  # Deploy to stage when triggered manually
#         steps:
#           - task: NodeTool@0
#             inputs:
#               versionSpec: '20.x'
#             displayName: 'Install Node.js'

#           - script: |
#               npm install
#               npm run build
#               ls -la
#             displayName: 'npm install and build'

#           - task: AzureCLI@2
#             inputs:
#               azureSubscription: 'frontendconnection'
#               scriptType: 'bash'
#               scriptLocation: 'inlineScript'
#               inlineScript: |
#                 az storage blob upload-batch \
#                   --account-name $(FRONTEND_STORAGE_ACCOUNT_STAGE) \
#                   --destination \$web \
#                   --source ./dist \
#                   --overwrite
#             displayName: 'Upload to Azure Blob Storage (Stage)'

#           - task: AzureCLI@2
#             inputs:
#               azureSubscription: 'frontendconnection'
#               scriptType: 'bash'
#               scriptLocation: 'inlineScript'
#               inlineScript: |
#                 az cdn endpoint purge \
#                   --resource-group $(RESOURCE_GROUP_STAGE) \
#                   --profile-name $(FRONTEND_CDN_PROFILE_STAGE) \
#                   --name $(FRONTEND_CDN_ENDPOINT_STAGE) \
#                   --content-paths '/*'
#             displayName: 'Purge Azure CDN Endpoint (Stage)'
