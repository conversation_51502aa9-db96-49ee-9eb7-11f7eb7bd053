import api from "../httpClient/api";

export const createNotificationTemplate = async (data: Record<string, any>) => {
  console.log(data,"createNotificationTemplate data");
  try {
    const response = await api.post("/user/create-email-notification-template", data); // Axios sets headers automatically
    
    console.log(response,"createNotificationTemplate response");
    return response.data;
  } catch (error) {
    console.error("Error creating system preferences:", error);
    throw error;
  }
};

export const getAllmailTemplates = async (

) => {
  const response = await api.get('/user/email-notification-templates');
  return response.data;
};

export const updateNotificationTemplate = async (id: string, data: Record<string, any>) => {
  try {
    const response = await api.post(`/user/update-email-notification-template/${id}`, data);
    
    return response.data;
  } catch (error) {
    console.error("Error updating system preferences:", error);
    throw error;
  }
};
export const getNotificationTemplatebyId = async (id: string) => {
  try {
    const response = await api.get(`/user/email-notification-template-by-id/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching template by ID:", error);
    throw error;
  }
};


