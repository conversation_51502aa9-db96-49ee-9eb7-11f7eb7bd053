import {
  Box, Grid, Typography, Card, CardContent, MenuItem, Select, FormControl, InputLabel
} from '@mui/material';
import {
  YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON>hart, Pie, Cell,
  XAxis
} from 'recharts';
// import { getAllvendors } from '../../services/vendor/vendor';
// import { useMutation, useQuery } from '@tanstack/react-query';
// import { useMemo } from 'react';
import { useAuthStore } from '../../store/auth';
// import { login, LoginPayload, verifyTwoFactor } from '../../services/auth/authService';
import { getUserRole } from '../../utils/authUtils';

const Dashboard = () => {
  const barData = [
    { name: 'Mon', shortlisted: 1200, hired: 700 },
    { name: '<PERSON><PERSON>', shortlisted: 2100, hired: 1200 },
    { name: 'Wed', shortlisted: 1800, hired: 1000 },
    { name: 'Thu', shortlisted: 1400, hired: 600 },
    { name: 'Fri', shortlisted: 1700, hired: 800 },
  ];
  const { user } = useAuthStore();

  const pieData = [
    { name: 'Shortlisted', value: 55 },
    { name: 'Hired', value: 25 },
    { name: 'Remaining', value: 20 }
  ];

  const COLORS = ['#003f88', '#fdb462', '#dce6f1'];

const role = getUserRole();
console.log('Role:', role)

  // if (isLoading) return <Typography>Loading...</Typography>;


  if (user?.role === "business") {
    return (
      <Box sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom>
          Admin Dashboard
        </Typography>
        <Typography variant="subtitle1" gutterBottom>
          Trusted Partners Delivering Quality Products and Services
        </Typography>

        <Grid container spacing={2} mt={2}>
          {[
            { label: 'MDM', title: 'Master Data Management', value: '30k', color: '#e3f2fd' },
            { label: 'OM', title: 'Order Management', value: '3.5 Days', color: '#fff3e0' },
            { label: 'LM', title: 'Logistics Management', value: '92%', color: '#e8f5e9' },
            { label: 'CM', title: 'Contract Management', value: '3.5 Days', color: '#e3f2fd' },
            { label: 'VD', title: 'Vendor Development', value: '52k', color: '#f3e5f5' },
          ].map((item, idx) => (
            <Grid item xs={12} sm={6} md={2.4} key={idx}>
              <Card sx={{ backgroundColor: item.color }}>
                <CardContent>
                  <Typography variant="h6">{item.value}</Typography>
                  <Typography variant="body2">{item.label}</Typography>
                  <Typography variant="caption">{item.title}</Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3} mt={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6">Your Pie Chart</Typography>
                <FormControl variant="standard" fullWidth sx={{ mt: 2, mb: 2 }}>
                  <InputLabel>View</InputLabel>
                  <Select defaultValue="Monthly">
                    <MenuItem value="Monthly">Monthly</MenuItem>
                    <MenuItem value="Weekly">Weekly</MenuItem>
                  </Select>
                </FormControl>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={pieData}
                      dataKey="value"
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      label
                    >
                      {pieData.map((_entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index]} />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6">Vendor Management</Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={barData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="shortlisted" stroke="#003f88" />
                    <Line type="monotone" dataKey="hired" stroke="#fdb462" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    );
  }

  if (user?.role === "vendor") {
    return (
      <Box sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom>
          Vendor Dashboard
        </Typography>
        <Typography variant="subtitle1" gutterBottom>
          Welcome to your performance overview
        </Typography>

        <Grid container spacing={2} mt={2}>
          <Grid item xs={12} sm={6}>
            <Card sx={{ backgroundColor: '#e0f7fa' }}>
              <CardContent>
                <Typography variant="h6">Tasks Completed</Typography>
                <Typography variant="h4">120</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Card sx={{ backgroundColor: '#fce4ec' }}>
              <CardContent>
                <Typography variant="h6">Pending Approvals</Typography>
                <Typography variant="h4">8</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Grid container spacing={3} mt={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6">Weekly Performance</Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={barData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="shortlisted" stroke="#0288d1" />
                    <Line type="monotone" dataKey="hired" stroke="#c2185b" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    );
  }

  return <Typography>Unauthorized Role</Typography>;
};

export default Dashboard;
