import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Divider,
  useTheme,
  Collapse,
} from "@mui/material";
import {
  Dashboard,
  NotificationsActive,
  People,
  Settings,
  Security,
  ExpandLess,
  ExpandMore,
  VpnKey,
  Storage,
  AssignmentInd,
  Straighten,
  EuroSymbol,
  VerifiedUser,
} from "@mui/icons-material";
import History from "@mui/icons-material/History";
import ContactsIcon from "@mui/icons-material/Contacts";
import { useAuthStore } from "../../store/auth";
import { useReactiveLogo } from "../../hooks/useSystemPreferences";
import { getFilteredNavItems } from "../../utils";

interface SideNavProps {
  open: boolean;
  drawerWidth: number;
  handleDrawerToggle: () => void;
  mobileOpen: boolean;
  collapseChildren?: boolean;
}

const SideNav: React.FC<SideNavProps> = ({
  open,
  drawerWidth,
  handleDrawerToggle,
  mobileOpen,
  collapseChildren,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  const { user } = useAuthStore();
  const reactiveLogo = useReactiveLogo();
  const BASE_URL = import.meta.env.VITE_ASSETS_BASE_URL || "";

  const [rolesManagementOpen, setRolesManagementOpen] = useState(
    location.pathname.startsWith("/roles-management")
  );
  const [masterDataManagementOpen, setMasterDataManagementOpen] = useState(
    location.pathname.startsWith("/master-data-management")
  );

  useEffect(() => {
    if (collapseChildren) {
      setRolesManagementOpen(false);
      setMasterDataManagementOpen(false);
    }
  }, [collapseChildren]);

  useEffect(() => {
    if (location.pathname.startsWith("/roles-management"))
      setRolesManagementOpen(true);
    if (location.pathname.startsWith("/master-data-management"))
      setMasterDataManagementOpen(true);
  }, [location.pathname]);

  const navItems = [
    {
      text: t("navigation.dashboard"),
      icon: <Dashboard />,
      path: "/dashboard",
      roles: ["business", "vendor"],
    },
    {
      text: t("navigation.rolesManagement"),
      icon: <Security />,
      roles: ["business"],
      isParent: true,
      children: [
        {
          text: t("navigation.roles"),
          icon: <Security />,
          path: "/roles-management/roles",
          roles: ["business"],
        },
        {
          text: t("navigation.permissions"),
          icon: <VpnKey />,
          path: "/roles-management/permissions",
          roles: ["business"],
        },
        {
          text: t("navigation.assaignUserRoles"),
          icon: <AssignmentInd />,
          path: "/roles-management/user-roles",
          roles: ["business"],
        },
        {
          text: t("navigation.assignRolePermissions"),
          icon: <VerifiedUser />,
          path: "/roles-management/role-permissions",
          roles: ["business"],
        },
        {
          text: t("navigation.roleAuditHistory"),
          icon: <History />,
          path: "/roles-management/role-audit-history",
          roles: ["business"],
        },
      ],
    },
    {
      text: t("navigation.masterDataManagement"),
      icon: <Storage />,
      roles: ["business"],
      isParent: true,
      children: [
        {
          text: t("navigation.vendor"),
          icon: <People />,
          path: "/master-data-management/users",
          roles: ["business"],
        },
        {
          text: t("navigation.PMIInternalContacts"),
          icon: <ContactsIcon />,
          path: "/master-data-management/internal-contacts",
          roles: ["business"],
        },
        {
          text: t("navigation.uom"),
          icon: <Straighten />,
          path: "/master-data-management/uom",
          roles: ["business"],
        },
        {
          text: "Currency",
          icon: <EuroSymbol />,
          path: "/master-data-management/currency",
          roles: ["business"],
        },
      ],
    },
    {
      text: t("navigation.notificationPreferences"),
      icon: <NotificationsActive />,
      path: "/notification-preferences",
      roles: ["business"],
    },
    {
      text: t("navigation.systemPreferences"),
      icon: <Settings />,
      path: "/system-preferences",
      roles: ["business"],
    },
    {
      text: t("navigation.vendor"),
      icon: <People />,
      path: "/vendor-onboarding/company-details",
      roles: ["vendor"],
    },
    {
      text: t("navigation.PMIInternalContacts"),
      icon: <ContactsIcon />,
      path: `/pmi-contacts`,
      roles: ["vendor"],
    },
  ];

  const typeOfUser = user?.role || "";
  const allowedNavItems = getFilteredNavItems(navItems, typeOfUser);

  const drawerContent = (
    <>
      <Toolbar
        sx={{
          justifyContent: "center",
          p: 2,
          background: "white",
          borderRadius: 1,
          m: 2,
        }}
      >
        <Box display="flex" flexDirection="column" alignItems="center">
          <img
            src={reactiveLogo}
            alt="Logo"
            onError={(e) => {
              (e.target as HTMLImageElement).src = `${BASE_URL}logo.png`;
            }}
            style={{ height: 48, marginBottom: 8, objectFit: "contain" }}
          />
        </Box>
      </Toolbar>
      <Divider />
      <List>
        {allowedNavItems.map((item) => (
          <React.Fragment key={item.text}>
            <ListItem disablePadding>
              <ListItemButton
                selected={item.path && location.pathname === item.path}
                onClick={() => {
                  if (item.isParent) {
                    if (item.text === t("navigation.rolesManagement"))
                      setRolesManagementOpen(!rolesManagementOpen);
                    if (item.text === t("navigation.masterDataManagement"))
                      setMasterDataManagementOpen(!masterDataManagementOpen);
                  } else if (item.path) {
                    navigate(item.path);
                  }
                }}
                sx={{
                  borderRadius: "12px",
                  mx: 1,
                  my: 0.5,
                  py: 1.2,
                  "&:hover": { backgroundColor: "#fff", color: "#005fa3" },
                  "&.Mui-selected": {
                    backgroundColor: "#fff",
                    color: "#004B8D",
                    "& .MuiListItemIcon-root": { color: "#004B8D" },
                  },
                }}
              >
                <ListItemIcon sx={{ color: "inherit", minWidth: 0, mr: 2 }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  sx={{
                    "& .MuiTypography-root": {
                      fontSize: "12px",
                      fontWeight: 600,
                      fontFamily:
                        "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
                    },
                  }}
                />
                {item.isParent &&
                  ((item.text === t("navigation.rolesManagement") &&
                    rolesManagementOpen) ||
                  (item.text === t("navigation.masterDataManagement") &&
                    masterDataManagementOpen) ? (
                    <ExpandLess />
                  ) : (
                    <ExpandMore />
                  ))}
              </ListItemButton>
            </ListItem>

            {item.isParent && item.children && (
              <Collapse
                in={
                  (item.text === t("navigation.rolesManagement") &&
                    rolesManagementOpen) ||
                  (item.text === t("navigation.masterDataManagement") &&
                    masterDataManagementOpen)
                }
                timeout="auto"
                unmountOnExit
              >
                <List component="div" disablePadding>
                  {item.children.map((child: any) => (
                    <ListItem key={child.text} disablePadding>
                      <ListItemButton
                        selected={location.pathname === child.path}
                        onClick={() => navigate(child.path)}
                        sx={{
                          borderRadius: "12px",
                          mx: 1,
                          my: 0.5,
                          py: 1.2,
                          pl: 5,
                          "&:hover": {
                            backgroundColor: "#fff",
                            color: "#005fa3",
                          },
                          "&.Mui-selected": {
                            backgroundColor: "#fff",
                            color: "#004B8D",
                            "& .MuiListItemIcon-root": { color: "#004B8D" },
                          },
                        }}
                      >
                        <ListItemIcon
                          sx={{ color: "inherit", minWidth: 0, mr: 2 }}
                        >
                          {child.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={child.text}
                          sx={{
                            "& .MuiTypography-root": {
                              fontSize: "12px",
                              fontWeight: 500,
                              fontFamily:
                                "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
                            },
                          }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>
    </>
  );

  return (
    <Box
      component="nav"
      sx={{
        width: { sm: drawerWidth },
        flexShrink: { sm: 0 },
        borderRadius: "12px",
      }}
    >
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: "block", sm: "none" },
          "& .MuiDrawer-paper": {
            backgroundColor: "#004B8D",
            color: "#fff",
            boxSizing: "border-box",
            width: drawerWidth,
          },
        }}
      >
        {drawerContent}
      </Drawer>
      <Drawer
        variant="permanent"
        open={open}
        sx={{
          display: { xs: "none", sm: "block" },
          "& .MuiDrawer-paper": {
            backgroundColor: "#004B8D",
            color: "#fff",
            width: drawerWidth,
            transition: theme.transitions.create("width", {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
            overflowX: "hidden",
            ...(open
              ? {}
              : {
                  width: theme.spacing(8),
                  transition: theme.transitions.create("width", {
                    easing: theme.transitions.easing.sharp,
                    duration: theme.transitions.duration.leavingScreen,
                  }),
                }),
          },
        }}
      >
        {drawerContent}
      </Drawer>
    </Box>
  );
};

export default SideNav;
