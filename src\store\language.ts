import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import i18n from '../utils/i18n';

interface LanguageState {
  currentLanguage: string;
  setLanguage: (language: string) => void;
  getAvailableLanguages: () => { code: string; name: string }[];
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set) => ({
      currentLanguage: i18n.language || 'en',

      setLanguage: (language) => {
        i18n.changeLanguage(language);
        set({ currentLanguage: language });
        localStorage.setItem('i18nextLng', language);
      },

      getAvailableLanguages: () => [
        { code: 'en', name: 'English' },
        { code: 'de', name: '<PERSON><PERSON><PERSON>' }
      ],
    }),
    {
      name: 'language-storage',
      partialize: (state) => ({ currentLanguage: state.currentLanguage }),
    }
  )
);