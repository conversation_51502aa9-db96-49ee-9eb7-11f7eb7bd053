import {
  useRoutes,
  useNavigate,
  useLocation,
  HashRouter,
} from "react-router-dom";
import { CssBaseline, ThemeProvider } from "@mui/material";
import { useEffect } from "react";
import getTheme from "./theme";
import routes from "./routes";
import "./utils/i18n";
import { useSessionTimeout } from "./utils/sessionTimeout";
import { useAuthStore } from "./store/auth";
import { clearPartialAuthState } from "./utils/authUtils";
import FaviconInitializer from "./components/common/FaviconInitializer";
import { SnackbarProvider } from "./utils/SnackbarProvider";

const AppRoutes = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, token } = useAuthStore();
  useSessionTimeout();
  useEffect(() => {
    clearPartialAuthState();
  }, []);

  useEffect(() => {
    const currentPath = location.pathname;
    const authData = localStorage.getItem("auth-storage");
    // const hasStoredAuth = authData && JSON.parse(authData)?.state?.token;
    const hasCompleted2FA =
      authData && JSON.parse(authData)?.state?.hasCompleted2FA;
    if (currentPath === "/") {
      if (isAuthenticated && token && hasCompleted2FA) {
        navigate("/dashboard");
      } else {
        navigate("/login");
      }
      return;
    }
  }, [location.pathname, isAuthenticated, token, navigate]);

  return useRoutes(routes);
};

function App() {
  return (
    <ThemeProvider theme={getTheme("light")}>
      <CssBaseline />
      <FaviconInitializer />
      <SnackbarProvider>
        {/* <BrowserRouter> */}
        <HashRouter>
          <AppRoutes />
        </HashRouter>
        {/* </BrowserRouter> */}
      </SnackbarProvider>
    </ThemeProvider>
  );
}

export default App;
