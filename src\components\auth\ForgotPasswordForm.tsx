import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import { forgotPassword } from '../../services/auth/authService';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { useLanguageStore } from '../../store/language';

const ForgotPasswordForm: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const navigate = useNavigate();

  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [success, setSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Store the type of error to help with re-translation
  const [errorType, setErrorType] = useState<'required' | 'email' | 'other' | ''>('');

  // Update error messages when language changes
  useEffect(() => {
    // Re-translate existing error messages when language changes
    if (emailError && errorType) {
      if (errorType === 'required') {
        setEmailError(t('forms.validation.required'));
      } else if (errorType === 'email') {
        setEmailError(t('forms.validation.email'));
      }
    }
  }, [currentLanguage, i18n.language, t, emailError, errorType]);

  const { mutate, isPending } = useMutation({
    mutationFn: forgotPassword,
    onSuccess: (response) => {

      if (response?.data?.success === false) {
        setErrorMessage(response.data.message || t('auth.invalidCredentials'));
        toast.error(response.data.message || t('auth.invalidCredentials'));
        return;
      }
      localStorage.setItem('resetPasswordEmail', email);

      setSuccess(true);
      toast.success(t('auth.resetLinkSent'));
    },
    onError: (err: any) => {

      if (err.response?.data?.message) {
        setErrorMessage(err.response.data.message);
      } else if (err.message) {
        setErrorMessage(err.message);
      } else {
        setErrorMessage(t('auth.somethingWentWrong'));
      }

      toast.error(errorMessage);
    }
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
    setErrorType('');
    setErrorMessage('');
  };

  const validateForm = () => {
    if (!email) {
      setEmailError(t('forms.validation.required'));
      setErrorType('required');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError(t('forms.validation.email'));
      setErrorType('email');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
        setErrorMessage('');

    if (!validateForm()) {
      return;
    }

    mutate(email);
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  if (success) {
    return (
      <Box sx={{ mt: 1, textAlign: 'center' }}>
        <Alert severity="success" sx={{ mb: 2 }}>
          {t('auth.resetLinkSent')}
        </Alert>

        {/* <Typography variant="body1" sx={{ mb: 2 }}>
          {t('auth.checkEmailForResetLink')}
        </Typography> */}

        <Button
          fullWidth
          variant="contained"
          onClick={handleBackToLogin}
          sx={{ mt: 2, py: 1.5 }}
        >
          {t('auth.backToLogin')}
        </Button>
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
      <Typography variant="body1" sx={{ mb: 2 }}>
        {t('auth.forgotPasswordDescription')}
      </Typography>

      {errorMessage && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorMessage}
        </Alert>
      )}

      {emailError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {emailError}
        </Alert>
      )}

      <TextField
        margin="normal"
        required
        fullWidth
        id="email"
        label={t('auth.email')}
        name="email"
        autoComplete="email"
        autoFocus
        value={email}
        onChange={handleChange}
        error={!!emailError || !!errorMessage}
        helperText={emailError}
        disabled={isPending}
        placeholder={t('forms.placeholders.enterEmail')}
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={isPending}
        sx={{ mt: 3, mb: 2, py: 1.5 }}
      >
        {isPending ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          t('auth.sendResetLink')
        )}
      </Button>

      <Button
        fullWidth
        variant="outlined"
        onClick={handleBackToLogin}
        disabled={isPending}
        sx={{ mb: 2, py: 1.5 }}
      >
        {t('auth.backToLogin')}
      </Button>
    </Box>
  );
};

export default ForgotPasswordForm;