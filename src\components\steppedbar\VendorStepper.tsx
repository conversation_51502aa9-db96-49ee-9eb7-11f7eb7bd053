import React from "react";
import {
  Stepper,
  Step,
  Step<PERSON>abel,
  StepConnector,
  StepIconProps,
  styled,
  stepConnectorClasses,
} from "@mui/material";
import Check from "@mui/icons-material/Check";

const steps = [
  "Company Profile",
  "Contact Details",
  "Location",
  "Payment & Delivery",
  "Certifications",
  "Review & Submit",
];

const CustomConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 16,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: "#004c97",
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: "#004c97",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor: "#e0e0e0",
    borderRadius: 1,
  },
}));

const CustomStepIcon = (props: StepIconProps) => {
  const { active, completed, icon } = props;

  return (
    <div
      style={{
        backgroundColor: completed || active ? "#004c97" : "#ccc",
        color: "white",
        width: 32,
        height: 32,
        borderRadius: "50%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontWeight: "bold",
      }}
    >
      {completed ? <Check fontSize="small" /> : icon}
    </div>
  );
};

interface VendorStepperProps {
  activeStep: number;
}

const VendorStepper: React.FC<VendorStepperProps> = ({ activeStep }) => {
  return (
    <Stepper
      alternativeLabel
      activeStep={activeStep}
      connector={<CustomConnector />}
      sx={{ marginBottom: 4 }}
    >
      {steps.map((label) => (
        <Step key={label}>
          <StepLabel StepIconComponent={CustomStepIcon}>{label}</StepLabel>
        </Step>
      ))}
    </Stepper>
  );
};

export default VendorStepper;
