import api from "../httpClient/api";



export interface LoginPayload {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export const login = async (data: LoginPayload) => {
  const response = await api.post('/user/signin', {
    email: data.email,
    password: data.password,
  });
  return response;
};
export const verifyTwoFactor = async (data: {
  code: string;
  email: string;
}): Promise<unknown> => {

  // eslint-disable-next-line no-useless-catch
  try {
    const response = await api.post('/user/verify2FA', data);
    return response;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const verifyTwoFactorVendor = async (data: {
  code: string;
  email: string;
}): Promise<unknown> => {

  // eslint-disable-next-line no-useless-catch
  try {
    const response = await api.post('/user/verify2fa-vendor', data);
    return response;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const forgotPassword = async (email: string): Promise<any> => {
  const response = await api.post('/user/forgotPassword', { email });
  return response;
};
export const vendorForgotPassword = async (email: string): Promise<any> => {
  const response = await api.post('/user/forgot-password-vendor', { email });
  return response;
};
export const resetPassword = async (email: string, token: string, password: string): Promise<any> => {
  const response = await api.post(`/user/resetPassword/${email}/${token}`, { email, token, password });
  return response;
};

export const resetPasswordVendor = async (email: string, token: string, password: string): Promise<any> => {
  const response = await api.post(`/user/reset-password-vendor/${email}/${token}`, { email, token, password });
  return response;
};
export const ViewallUsers = async () => {
  const response = await api.get('/user/view-users');
  return response.data;
};
export const ViewallVendors = async () => {
  const response = await api.get('/user/view-vendors');
  return response.data;
};
export const assignUserRole = async ({ userId, data }: { userId: number; data: { roleIds: number[],userType: string } }) => {
  return api.patch(`/user/assign-roles/${userId}`, data);
};

// export const unAssignRole = async ({ userId, data }: { userId: number; data: { roleIds: number[] } }) => {
//    return api.delete(`/user/unassign-roles/${userId}`, data);
// };

export const unAssignRole = async ({
  userId,
  data,
}: {
  userId: number;
  data: { roleIds: number[],userType: string  };
}) => {
  return api.delete(`/user/unassign-roles/${userId}`, {
    data, // <---- ✅ wrap in `data` inside config object
  });
};
