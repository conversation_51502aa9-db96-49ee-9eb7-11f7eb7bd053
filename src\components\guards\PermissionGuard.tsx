import React from 'react';
import { useAuthStore } from '../../store/auth';

interface PermissionGuardProps {
  children: React.ReactNode;
  requiredPermissions: string[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
  fallback?: React.ReactNode;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({ 
  children, 
  requiredPermissions, 
  requireAll = false,
  fallback = null 
}) => {
  const { user } = useAuthStore();

  // Check if user has the required permissions
  const hasPermission = () => {
    if (!user) return false;

    // Get user permissions from roles
    const userPermissions: string[] = [];
    
    if (user.roles && Array.isArray(user.roles)) {
      user.roles.forEach((role: any) => {
        if (role.permissions && Array.isArray(role.permissions)) {
          role.permissions.forEach((permission: any) => {
            userPermissions.push(permission.permission_name || permission.name);
          });
        }
      });
    }

    // If user has direct permissions (fallback)
    if (user.permissions && Array.isArray(user.permissions)) {
      user.permissions.forEach((permission: any) => {
        userPermissions.push(permission.permission_name || permission.name);
      });
    }

    // Admin role has all permissions
    if (user.role === 'admin') return true;

    if (requireAll) {
      // User must have ALL required permissions
      return requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );
    } else {
      // User must have ANY of the required permissions
      return requiredPermissions.some(permission => 
        userPermissions.includes(permission)
      );
    }
  };

  if (!hasPermission()) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default PermissionGuard;
