import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { CardContent, Container, Typography, Box, Paper } from "@mui/material";
import { useAuthStore } from "../../store/auth";
import { useReactiveLogo } from "../../hooks/useSystemPreferences";
import ChangePasswordForm from "../../components/auth/ChangePasswordForm";
import LanguageSwitcher from "../../components/layout/LanguageSwitcher";
import VendorChangePasswordForm from "../../components/auth/VendorChangePasswordForm";

const VendorChangePasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, hasCompleted2FA, token ,user} = useAuthStore();
  const reactiveLogo = useReactiveLogo();
  const BASE_URL = import.meta.env.VITE_ASSETS_BASE_URL || '';
  const email = user?.email || location.state?.email || localStorage.getItem("passWordEmail") || "";
console.log("ChangePasswordPage - Environment check:", user?.email)

  useEffect(() => {
   
  }, [
    location.state,
    navigate,
    isAuthenticated,
    hasCompleted2FA,
    token,
    user
  ]);


  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: (theme) => theme.palette.grey[50],
      }}
    >
      <Container maxWidth="sm">
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            mb: 2,
          }}
        >
          <LanguageSwitcher />
        </Box>

        <Paper
          elevation={6}
          sx={{
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              bgcolor: "primary.main",
              py: 3,
              px: 4,
              color: "white",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Box
              component="img"
              src={reactiveLogo}
              alt="Logo"
              sx={{
                height: 48,
                mb: 2,
                objectFit: 'contain',
                maxWidth: '100%'
              }}
              onError={(e) => {
                console.error('Logo failed to load:', reactiveLogo);
                // Fallback to default logo
                const target = e.target as HTMLImageElement;
                target.src = `${BASE_URL}logo.png`;
              }}
            />
            <Typography variant="h4" component="h1" gutterBottom>
              {t("auth.changePassword")}
            </Typography>
          </Box>

          <CardContent sx={{ py: 4, px: { xs: 3, sm: 4 } }}>
            <VendorChangePasswordForm email={email} />
          </CardContent>
        </Paper>
      </Container>
    </Box>
  );
};

export default VendorChangePasswordPage;
