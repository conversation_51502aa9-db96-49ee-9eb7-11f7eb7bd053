// src/utils/exportToCSV.ts
export const exportToCSV = (data: any[], filename: string = "export.csv") => {
  if (!data.length) return;

  const keys = Object.keys(data[0]);
  const csvRows = [
    keys.join(","), // header row
    ...data.map((row) =>
      keys.map((key) => `"${(row[key] ?? "").toString().replace(/"/g, '""')}"`).join(",")
    ),
  ];

  const blob = new Blob([csvRows.join("\n")], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);

  const a = document.createElement("a");
  a.setAttribute("hidden", "");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};
