import { useState, useEffect, useMemo } from "react";
import { Add, Edit, Delete } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  IconButton,
  Box,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import _ from "lodash";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import { SortDirection } from "../../data/constants";
import CloseIcon from "@mui/icons-material/Close";
import {
  getAllUoMs,
  createUoM,
  updateUoM,
  deleteUoM,
  toggleUoMStatus,
  UoM,
} from "../../services/uom/uom";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import Loader from "../../utils/Loader";

function UomPage() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { currentLanguage } = useLanguageStore();
  const { user } = useAuthStore();

  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedUoM, setSelectedUoM] = useState<UoM | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [uomToDelete, setUoMToDelete] = useState<UoM | null>(null);

  const [formData, setFormData] = useState({
    uom_code: "",
    uom_name: "",
    uom_alias: "",
    isActive: true,
  });

  const [formErrors, setFormErrors] = useState({
    uom_code: "",
    uom_name: "",
    uom_alias: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error",
  });

  // Pagination and sorting state
  const [page, setPage] = useState(0);
  const [pageCount, setPageCount] = useState(10);
  const [sort, setSort] = useState<any>({});

  // Fetch UoMs
  const { data: uomsResponse, isLoading } = useQuery({
    queryKey: ["uoms", page, pageCount, sort],
    queryFn: getAllUoMs,
  });

  const uomsData = uomsResponse?.result || [];
  const totalCount = uomsResponse?.totalCount || 0;

  // Create UoM mutation
  const createUoMMutation = useMutation({
    mutationFn: createUoM,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      setSnackbar({
        open: true,
        message: t("uom.uomCreated"),
        severity: "success",
      });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });

  // Update UoM mutation
  const updateUoMMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<UoM> }) =>
      updateUoM(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      setSnackbar({
        open: true,
        message: t("uom.uomUpdated"),
        severity: "success",
      });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });

  // Delete UoM mutation
  const deleteUoMMutation = useMutation({
    mutationFn: deleteUoM,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      setSnackbar({
        open: true,
        message: t("uom.uomDeleted"),
        severity: "success",
      });
      setDeleteDialogOpen(false);
      setUoMToDelete(null);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setSnackbar({
        open: true,
        message: errorMessage,
        severity: "error",
      });
    },
  });

  // Toggle status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
      toggleUoMStatus(id, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setSnackbar({
        open: true,
        message: errorMessage,
        severity: "error",
      });
    },
  });

  // Table columns
  const defaultColumns: ColumnType[] = useMemo(
    () => [
      {
        title: t("uom.uomCode"),
        key: "uom_code",
        // sort: true,
        searchable: true,
        filterable: true,
        width: "150px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("uom.uomName"),
        key: "uom_name",
        // sort: true,
        searchable: true,
        filterable: true,
        width: "200px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("uom.uomAlias"),
        key: "uom_alias",
        // sort: true,
        searchable: true,
        filterable: true,
        width: "150px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("uom.status"),
        key: "isActive",
        width: "120px",
        direction: SortDirection.ASC,
        active: false,
        render: (item: UoM) =>
          item.isActive ? (
            <Checkbox checked color="primary" disabled />
          ) : (
            <Checkbox
              icon={<CloseIcon style={{ color: "grey" }} />}
              checkedIcon={<CloseIcon style={{ color: "grey" }} />}
              disabled
              checked
            />
          ),
      },
      {
        title: t("common.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        width: "120px",
        render: (item: UoM) => (
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => handleEdit(item)}
              color="primary"
            >
              <Edit fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => handleDeleteClick(item)}
              color="error"
            >
              <Delete fontSize="small" />
            </IconButton>
          </Box>
        ),
      },
    ],
    [t, currentLanguage, toggleStatusMutation]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const validateForm = () => {
    const errors = {
      uom_code: "",
      uom_name: "",
      uom_alias: "",
    };

    if (!formData.uom_code.trim()) {
      errors.uom_code = t("uom.uomCodeRequired");
    }

    if (!formData.uom_name.trim()) {
      errors.uom_name = t("uom.uomNameRequired");
    }

    if (!formData.uom_alias.trim()) {
      errors.uom_alias = t("uom.uomAliasRequired");
    }

    setFormErrors(errors);
    return !Object.values(errors).some((error) => error !== "");
  };

  const handleSubmit = async () => {
    setGeneralError("");

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const payload: any = {
        uom_code: formData.uom_code.trim(),
        uom_name: formData.uom_name.trim(),
        uom_alias: formData.uom_alias.trim(),
        isActive: formData.isActive,
      };

      if (editMode && selectedUoM) {
        updateUoMMutation.mutate({ id: selectedUoM.id!, data: payload });
      } else {
        createUoMMutation.mutate(payload);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (uom: UoM) => {
    setSelectedUoM(uom);
    setFormData({
      uom_code: uom.uom_code,
      uom_name: uom.uom_name,
      uom_alias: uom.uom_alias,
      isActive: uom.isActive,
    });
    setEditMode(true);
    setOpen(true);
    setGeneralError("");
    setFormErrors({ uom_code: "", uom_name: "", uom_alias: "" });
  };

  const handleDeleteClick = (uom: UoM) => {
    setUoMToDelete(uom);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (uomToDelete) {
      deleteUoMMutation.mutate(uomToDelete.id!);
    }
  };

  const resetForm = () => {
    setFormData({ uom_code: "", uom_name: "", uom_alias: "", isActive: true });
    setFormErrors({ uom_code: "", uom_name: "", uom_alias: "" });
    setGeneralError("");
    setEditMode(false);
    setSelectedUoM(null);
  };

  const handleCreateNew = () => {
    resetForm();
    setOpen(true);
  };

  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Box m={2}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h4">{t("uom.title")}</Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateNew}
          >
            {t("uom.createNew")}
          </Button>
        </Box>

        <Table
         sx={{height:'auto'}}
          columns={columns}
          data={uomsData || []}
          sortHandler={handleSort}
          pagination={{
            totalCount,
            pageCount,
            setPageCount,
            page,
            setPage,
          }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />
      </Box>

      {/* UoM Create/Edit Dialog */}
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {editMode ? t("uom.editUom") : t("uom.createUom")}
        </DialogTitle>
        <DialogContent dividers>
          {generalError && <Alert severity="error">{generalError}</Alert>}

          <TextField
            autoFocus
            margin="dense"
            label={t("uom.uomCode")}
            fullWidth
            variant="outlined"
            value={formData.uom_code}
            onChange={(e) =>
              setFormData({ ...formData, uom_code: e.target.value })
            }
            error={!!formErrors.uom_code}
            helperText={formErrors.uom_code}
            placeholder={t("uom.uomCodePlaceholder")}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label={t("uom.uomName")}
            fullWidth
            variant="outlined"
            value={formData.uom_name}
            onChange={(e) =>
              setFormData({ ...formData, uom_name: e.target.value })
            }
            error={!!formErrors.uom_name}
            helperText={formErrors.uom_name}
            placeholder={t("uom.uomNamePlaceholder")}
            sx={{ mb: 2 }}
          />

          <TextField
            margin="dense"
            label={t("uom.uomAlias")}
            fullWidth
            variant="outlined"
            value={formData.uom_alias}
            onChange={(e) =>
              setFormData({ ...formData, uom_alias: e.target.value })
            }
            error={!!formErrors.uom_alias}
            helperText={formErrors.uom_alias}
            placeholder={t("uom.uomAliasPlaceholder")}
            sx={{ mb: 2 }}
          />

          <FormControlLabel
            control={
              <Checkbox
                checked={formData.isActive}
                onChange={(e) =>
                  setFormData({ ...formData, isActive: e.target.checked })
                }
                color="primary"
              />
            }
            label={t("uom.isActive")}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)} color="inherit">
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting}
          >
            {isSubmitting
              ? t("common.loading")
              : editMode
              ? t("common.update")
              : t("common.create")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t("uom.deleteUom")}</DialogTitle>
        <DialogContent>
          <Typography>{t("uom.confirmDeleteUom")}</Typography>
          {uomToDelete && (
            <Typography variant="body2" sx={{ mt: 1, fontWeight: "bold" }}>
              {uomToDelete.uom_name} ({uomToDelete.uom_code})
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} color="inherit">
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteUoMMutation.isPending}
          >
            {deleteUoMMutation.isPending
              ? t("common.loading")
              : t("common.delete")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <MuiAlert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </MuiAlert>
      </Snackbar>
    </>
  );
}

export default UomPage;
