import React, { useState, useEffect, useMemo } from "react";
import { Add, Edit, Delete } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  IconButton,
  Box,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
} from "@mui/material";
import _ from "lodash";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import { getAllVendors, createVendor, updateVendor, deleteVendor, Vendor } from "../../services/vendors/vendors";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import Loader from "../../utils/Loader";
import { formatDate } from "../../utils/dateFormats";
import { getAllGlobalSystemPreferences } from "../../services/preferences/preferences";

function VendorsPage() {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const queryClient = useQueryClient();
  const [page, setPage] = useState<number>(0);
  const [pageCount, setPageCount] = useState<number>(10);
  const [sort, setSort] = useState<any>({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
  const { user } = useAuthStore();

  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [vendorToDelete, setVendorToDelete] = useState<Vendor | null>(null);

  const [formData, setFormData] = useState({
    vendor_name: "",
    vendor_email: "",
    vendor_phone: "",
    vendor_address: "",
    vendor_city: "",
    vendor_state: "",
    vendor_country: "",
    vendor_zip_code: "",
    vendor_website: "",
    vendor_description: "",
    vendor_type: "",
    vendor_status: "active" as "active" | "inactive",
    contact_person: "",
    tax_id: "",
    business_license: "",
    service_category: "",
  });

  const [formErrors, setFormErrors] = useState({
    vendor_name: "",
    vendor_email: "",
    vendor_phone: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch vendors
  const { data: vendorsResponse, isLoading } = useQuery({
    queryKey: ["vendors"],
    queryFn: getAllVendors,
  });

  const vendorsData = vendorsResponse || [];
  const totalCount = vendorsData.length;

  // Fetch preferences for date formatting
  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: getAllGlobalSystemPreferences,
  });

  // Create vendor mutation
  const createVendorMutation = useMutation({
    mutationFn: createVendor,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] });
      setSnackbarMessage(t("vendors.vendorCreated"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });

  // Update vendor mutation
  const updateVendorMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Vendor> }) => updateVendor(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] });
      setSnackbarMessage(t("vendors.vendorUpdated"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });

  // Delete vendor mutation
  const deleteVendorMutation = useMutation({
    mutationFn: deleteVendor,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] });
      setSnackbarMessage(t("vendors.vendorDeleted"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      setDeleteDialogOpen(false);
      setVendorToDelete(null);
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || t("common.error");
      setSnackbarMessage(errorMessage);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    },
  });

  const defaultColumns: ColumnType[] = useMemo(
    () => [
      {
        title: t("vendors.vendorName"),
        key: "vendor_name",
        searchable: true,
        filterable: true,
        render: (row: Vendor) => row.vendor_name,
      },
      {
        title: t("vendors.vendorEmail"),
        key: "vendor_email",
        searchable: true,
        filterable: true,
        render: (row: Vendor) => row.vendor_email,
      },
      {
        title: t("vendors.vendorPhone"),
        key: "vendor_phone",
        searchable: true,
        filterable: true,
        render: (row: Vendor) => row.vendor_phone || "-",
      },
      {
        title: t("vendors.vendorCity"),
        key: "vendor_city",
        searchable: true,
        filterable: true,
        render: (row: Vendor) => row.vendor_city || "-",
      },
      {
        title: t("vendors.vendorStatus"),
        key: "vendor_status",
        searchable: false,
        filterable: true,
        render: (row: Vendor) => (
          <Typography
            variant="body2"
            sx={{
              color: row.vendor_status === "active" ? "success.main" : "error.main",
              fontWeight: "medium",
            }}
          >
            {row.vendor_status === "active" ? t("common.active") : t("common.inactive")}
          </Typography>
        ),
      },
      {
        title: t("common.createdAt"),
        key: "createdAt",
        searchable: false,
        filterable: false,
        render: (row: Vendor) => formatDate(row.createdAt, preferences?.result?.date_format, currentLanguage),
      },
      {
        title: t("common.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        render: (row: Vendor) => (
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => handleEdit(row)}
              aria-label="Edit vendor"
            >
              <Edit />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => handleDeleteClick(row)}
              aria-label="Delete vendor"
              color="error"
            >
              <Delete />
            </IconButton>
          </Box>
        ),
      },
    ],
    [t, currentLanguage, preferences]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const errors = {
      vendor_name: "",
      vendor_email: "",
      vendor_phone: "",
    };

    if (!formData.vendor_name.trim()) {
      errors.vendor_name = t("vendors.vendorNameRequired");
    }

    if (!formData.vendor_email.trim()) {
      errors.vendor_email = t("vendors.vendorEmailRequired");
    } else if (!/\S+@\S+\.\S+/.test(formData.vendor_email)) {
      errors.vendor_email = t("auth.invalidEmail");
    }

    setFormErrors(errors);
    return !Object.values(errors).some(error => error !== "");
  };

  const handleSubmit = async () => {
    setGeneralError("");

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const payload: any = {
        vendor_name: formData.vendor_name,
        vendor_email: formData.vendor_email,
        vendor_phone: formData.vendor_phone,
        vendor_address: formData.vendor_address,
        vendor_city: formData.vendor_city,
        vendor_state: formData.vendor_state,
        vendor_country: formData.vendor_country,
        vendor_zip_code: formData.vendor_zip_code,
        vendor_website: formData.vendor_website,
        vendor_description: formData.vendor_description,
        vendor_type: formData.vendor_type,
        vendor_status: formData.vendor_status,
        contact_person: formData.contact_person,
        tax_id: formData.tax_id,
        business_license: formData.business_license,
        service_category: formData.service_category,
      };

      if (editMode && selectedVendor) {
        updateVendorMutation.mutate({ id: selectedVendor.id!, data: payload });
      } else {
        createVendorMutation.mutate(payload);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (vendor: Vendor) => {
    setSelectedVendor(vendor);
    setFormData({
      vendor_name: vendor.vendor_name,
      vendor_email: vendor.vendor_email,
      vendor_phone: vendor.vendor_phone || "",
      vendor_address: vendor.vendor_address || "",
      vendor_city: vendor.vendor_city || "",
      vendor_state: vendor.vendor_state || "",
      vendor_country: vendor.vendor_country || "",
      vendor_zip_code: vendor.vendor_zip_code || "",
      vendor_website: vendor.vendor_website || "",
      vendor_description: vendor.vendor_description || "",
      vendor_type: vendor.vendor_type || "",
      vendor_status: vendor.vendor_status || "active",
      contact_person: vendor.contact_person || "",
      tax_id: vendor.tax_id || "",
      business_license: vendor.business_license || "",
      service_category: vendor.service_category || "",
    });
    setEditMode(true);
    setOpen(true);
    setGeneralError("");
    setFormErrors({ vendor_name: "", vendor_email: "", vendor_phone: "" });
  };

  const handleDeleteClick = (vendor: Vendor) => {
    setVendorToDelete(vendor);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (vendorToDelete) {
      deleteVendorMutation.mutate(vendorToDelete.id!);
    }
  };

  const resetForm = () => {
    setFormData({
      vendor_name: "",
      vendor_email: "",
      vendor_phone: "",
      vendor_address: "",
      vendor_city: "",
      vendor_state: "",
      vendor_country: "",
      vendor_zip_code: "",
      vendor_website: "",
      vendor_description: "",
      vendor_type: "",
      vendor_status: "active",
      contact_person: "",
      tax_id: "",
      business_license: "",
      service_category: "",
    });
    setFormErrors({ vendor_name: "", vendor_email: "", vendor_phone: "" });
    setGeneralError("");
    setEditMode(false);
    setSelectedVendor(null);
  };

  const handleCreateNew = () => {
    resetForm();
    setOpen(true);
  };

  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "admin") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Box p={3} pb={0} px={1}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mt={2}
        >
          <Typography variant="subtitle1" color="primary">
            {t("vendors.title")}
          </Typography>
          <Box mb={2}>
            <Button
              onClick={handleCreateNew}
              variant="contained"
              startIcon={<Add />}
              color="secondary"
            >
              {t("vendors.createNew")}
            </Button>
          </Box>
        </Box>

        <Box>
          <Table
            sx={{ pb: 0, mb: 0, height: "auto" }}
            pagination={{
              totalCount,
              pageCount,
              setPageCount,
              page,
              setPage,
            }}
            data={vendorsData || []}
            columns={columns}
            loading={isLoading}
            sortHandler={handleSort}
            enableSearch={true}
            enableFilter={true}
            searchPlaceholder={t("table.searchPlaceholder")}
          />
        </Box>
      </Box>

      {/* Create/Edit Vendor Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)} fullWidth maxWidth="md">
        <DialogTitle>{editMode ? t("vendors.editVendor") : t("vendors.createVendor")}</DialogTitle>
        <DialogContent dividers>
          {generalError && <Alert severity="error">{generalError}</Alert>}

          <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: 2, mt: 1 }}>
            <TextField
              label={t("vendors.vendorName")}
              variant="outlined"
              fullWidth
              name="vendor_name"
              value={formData.vendor_name}
              onChange={handleChange}
              error={!!formErrors.vendor_name}
              helperText={formErrors.vendor_name}
              autoFocus
            />
            <TextField
              label={t("vendors.vendorEmail")}
              variant="outlined"
              fullWidth
              name="vendor_email"
              type="email"
              value={formData.vendor_email}
              onChange={handleChange}
              error={!!formErrors.vendor_email}
              helperText={formErrors.vendor_email}
            />
            <TextField
              label={t("vendors.vendorPhone")}
              variant="outlined"
              fullWidth
              name="vendor_phone"
              value={formData.vendor_phone}
              onChange={handleChange}
              error={!!formErrors.vendor_phone}
              helperText={formErrors.vendor_phone}
            />
            <TextField
              label={t("vendors.contactPerson")}
              variant="outlined"
              fullWidth
              name="contact_person"
              value={formData.contact_person}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.vendorAddress")}
              variant="outlined"
              fullWidth
              name="vendor_address"
              value={formData.vendor_address}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.vendorCity")}
              variant="outlined"
              fullWidth
              name="vendor_city"
              value={formData.vendor_city}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.vendorState")}
              variant="outlined"
              fullWidth
              name="vendor_state"
              value={formData.vendor_state}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.vendorCountry")}
              variant="outlined"
              fullWidth
              name="vendor_country"
              value={formData.vendor_country}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.vendorZipCode")}
              variant="outlined"
              fullWidth
              name="vendor_zip_code"
              value={formData.vendor_zip_code}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.vendorWebsite")}
              variant="outlined"
              fullWidth
              name="vendor_website"
              value={formData.vendor_website}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.vendorType")}
              variant="outlined"
              fullWidth
              name="vendor_type"
              value={formData.vendor_type}
              onChange={handleChange}
            />
            <FormControl fullWidth>
              <InputLabel>{t("vendors.vendorStatus")}</InputLabel>
              <Select
                name="vendor_status"
                value={formData.vendor_status}
                onChange={(e) => handleChange(e as any)}
                label={t("vendors.vendorStatus")}
              >
                <MenuItem value="active">{t("common.active")}</MenuItem>
                <MenuItem value="inactive">{t("common.inactive")}</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label={t("vendors.taxId")}
              variant="outlined"
              fullWidth
              name="tax_id"
              value={formData.tax_id}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.businessLicense")}
              variant="outlined"
              fullWidth
              name="business_license"
              value={formData.business_license}
              onChange={handleChange}
            />
            <TextField
              label={t("vendors.serviceCategory")}
              variant="outlined"
              fullWidth
              name="service_category"
              value={formData.service_category}
              onChange={handleChange}
            />
          </Box>
          <TextField
            label={t("vendors.vendorDescription")}
            variant="outlined"
            fullWidth
            margin="normal"
            name="vendor_description"
            value={formData.vendor_description}
            onChange={handleChange}
            multiline
            minRows={3}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>{t("common.cancel")}</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting}
          >
            {editMode ? t("common.update") : t("common.create")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>{t("vendors.deleteVendor")}</DialogTitle>
        <DialogContent>
          <Typography>
            {t("vendors.confirmDeleteVendor")}
          </Typography>
          {vendorToDelete && (
            <Typography variant="body2" sx={{ mt: 1, fontWeight: "bold" }}>
              {vendorToDelete.vendor_name}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>{t("common.cancel")}</Button>
          <Button
            onClick={handleDeleteConfirm}
            variant="contained"
            color="error"
            disabled={deleteVendorMutation.isPending}
          >
            {t("common.delete")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <MuiAlert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </MuiAlert>
      </Snackbar>
    </>
  );
}

export default VendorsPage;
