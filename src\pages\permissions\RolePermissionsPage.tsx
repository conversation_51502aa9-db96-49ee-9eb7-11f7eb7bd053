import React, { useState, useEffect, useMemo } from "react";
import {
  Box,
  Typography,
  IconButton,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  SelectChangeEvent,
} from "@mui/material";
import { Edit, Delete } from "@mui/icons-material";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import _ from "lodash";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import { useNavigate } from "react-router-dom";
import useQueryParams from "../../hooks/useQueryParams";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import { formatDate } from "../../utils/dateFormats";
import Loader from "../../utils/Loader";
import { getAllGlobalSystemPreferences } from "../../services/preferences/preferences";
import { getAllRolePermissions, assignRolePermissions, removeRolePermissions } from "../../services/rolePermissions/role-permission";
import { getAllRoles } from "../../services/roles/roles";
import { getAllPermissions } from "../../services/permissions/permissions";
import { StyledProfileNav, StyledProfileNavItem } from "../roles/styles";

const rolePermissionsTabs = [
  {
    titleKey: "rolePermissions.businessRoles",
    path: "business-roles",
    userType: "business" as const,
  },
  {
    titleKey: "rolePermissions.vendorRoles",
    path: "vendor-roles",
    userType: "vendor" as const,
  },
];

const AssignRolePermissions = () => {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { queryParams } = useQueryParams();
  const active: any = queryParams.tab || "business-roles";

  // Determine current tab
  const currentTab = rolePermissionsTabs.find(tab => tab.path === active) || rolePermissionsTabs[0];

  const [page, setPage] = useState(0);
  const [pageCount, setPageCount] = useState(10);
  const [sort, setSort] = useState<any>({});
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error",
  });

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Query global preferences
  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });

  // Query roles filtered by current tab's user type
  const { data: rolesData = [], isLoading } = useQuery({
    queryKey: ["roles", currentTab.userType, page, pageCount, sort],
    queryFn: async () => {
      const response = await getAllRolePermissions();
      const allRoles = response.result || [];
      // Filter roles based on current tab's userType
      return allRoles.filter((role: any) => role.userType === currentTab.userType);
    },
  });
  // Query all permissions filtered by current tab's user type
  const { data: permissionsData = [] } = useQuery({
    queryKey: ["permissions", currentTab.userType],
    queryFn: async () => {
      const response = await getAllPermissions();
      const allPermissions = response.result || [];
      // Filter permissions based on current tab's userType
      return allPermissions.filter((permission: any) => permission.userType === currentTab.userType);
    },
  });

  // Sorting handler
  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  // Open dialog to assign permissions to role
  const handleAssignPermissions = (role: any) => {
    setSelectedRole(role);
    const currentPermissionIds = role.permissions?.map((permission: any) => permission.id) || [];
    setSelectedPermissionIds(currentPermissionIds);
    setEditDialogOpen(true);
  };

  // Handle permission selection change
  const handlePermissionSelectionChange = (event: SelectChangeEvent<number[]>) => {
    const value = event.target.value;
    setSelectedPermissionIds(typeof value === "string" ? [] : (value as number[]));
  };

  // Handle delete role permissions
  const handleDeleteRolePermissions = (role: any) => {
    setSelectedRole(role);
    setDeleteDialogOpen(true);
  };

  // Delete mutation
  const deleteRolePermissionsMutation = useMutation({
    mutationFn: async (roleId: number) => {
      // Remove all permissions from role by setting empty array
      return await assignRolePermissions({ roleId, data: { permissionIds: [] } });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setSnackbar({
        open: true,
        message: t("rolePermissions.permissionsRemovedSuccessfully"),
        severity: "success",
      });
      setDeleteDialogOpen(false);
      setSelectedRole(null);
    },
    onError: (error: any) => {
      const msg = error?.response?.data?.message || t("common.unknownError");
      setSnackbar({ open: true, message: msg, severity: "error" });
    },
  });

  const handleSubmit = async () => {
    if (!selectedRole || selectedPermissionIds.length === 0) {
      setSnackbar({
        open: true,
        message: t("rolePermissions.selectAtLeastOnePermission"),
        severity: "error",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Send the whole permissionIds array at once
      await assignRolePermissions({
        roleId: selectedRole.id,
        data: { permissionIds: selectedPermissionIds },
      });

      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setSnackbar({
        open: true,
        message: t("rolePermissions.permissionsUpdated"),
        severity: "success",
      });
      handleDialogClose();
    } catch (error: any) {
      const msg = error?.response?.data?.message || t("common.unknownError");
      setSnackbar({ open: true, message: msg, severity: "error" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDialogClose = () => {
    setEditDialogOpen(false);
    setSelectedRole(null);
    setSelectedPermissionIds([]);
    setIsSubmitting(false);
  };

  const defaultColumns: ColumnType[] = useMemo(
    () => [
      {
        title: t("rolePermissions.roleName"),
        key: "role_name",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("rolePermissions.permissions"),
        key: "permissions",
        sort: false,
        render: (row) => {
          const permissions = row.permissions?.map((perm: any) => perm.permission_name).join(", ") || "-";
          return <span>{permissions}</span>;
        },
      },
      {
        title: t("rolePermissions.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        render: (row: any) => (
          <Box display="flex" gap={1}>
            <IconButton size="small" onClick={() => handleAssignPermissions(row)}>
              <Edit />
            </IconButton>
            {/* <IconButton
              size="small"
              onClick={() => handleDeleteRolePermissions(row)}
              color="error"
              disabled={!row.permissions || row.permissions.length === 0}
            >
              <Delete />
            </IconButton> */}
          </Box>
        ),
      },
    ],
    [t, currentLanguage, preferences]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  

  return (
    <>
      {/* Tab Navigation */}
      <Box sx={{ position: "relative", bgcolor: "white", zIndex: 2 }}>
        <StyledProfileNav>
          {rolePermissionsTabs.map((item, index) => (
            <StyledProfileNavItem
              key={index}
              onClick={() => navigate(`?tab=${item.path}`)}
              active={active === item.path ? 1 : 0}
            >
              {t(item.titleKey)}
            </StyledProfileNavItem>
          ))}
        </StyledProfileNav>
      </Box>

      <Box p={3}>
        <Typography variant="h6" color="primary" gutterBottom>
          {t("rolePermissions.title")} - {t(currentTab.titleKey)}
        </Typography>

        <Table
         sx={{height:'auto'}}
          data={rolesData}
          columns={columns}
          pagination={{
            totalCount: rolesData.length,
            pageCount,
            setPageCount,
            page,
            setPage,
          }}
          sortHandler={handleSort}
          loading={isLoading}
          enableSearch
          enableFilter
          searchPlaceholder={t("table.searchPlaceholder")}
        />

      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          severity={snackbar.severity}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      <Dialog
        open={editDialogOpen}
        onClose={handleDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {t("rolePermissions.editRolePermissions")} - {selectedRole?.role_name}
        </DialogTitle>
        <DialogContent dividers>
          <TextField
            label={t("rolePermissions.roleName")}
            value={selectedRole?.role_name || ""}
            fullWidth
            margin="normal"
            disabled
          />

          <FormControl fullWidth margin="normal">
            <InputLabel>{t("rolePermissions.selectPermissions")}</InputLabel>
            <Select
              multiple
              value={selectedPermissionIds}
              onChange={handlePermissionSelectionChange}
              input={<OutlinedInput label={t("rolePermissions.selectPermissions")} />}
              renderValue={(selected) => (
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                  {(selected as number[]).map((permissionId) => {
                    const permission = permissionsData.find((p: any) => p.id === permissionId);
                    return (
                      <Chip
                        key={permissionId}
                        label={permission?.permission_name || permissionId}
                        size="small"
                      />
                    );
                  })}
                </Box>
              )}
            >
              {permissionsData.map((permission: any) => (
                <MenuItem key={permission.id} value={permission.id}>
                  {permission.permission_name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>{t("common.cancel")}</Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            variant="contained"
          >
            {isSubmitting ? t("common.saving") : t("common.save")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>{t("rolePermissions.confirmDeleteTitle")}</DialogTitle>
        <DialogContent>
          <Typography>
            {t("rolePermissions.confirmDeleteMessage", {
              roleName: selectedRole?.role_name || ""
            })}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t("common.cancel")}
          </Button>
          <Button
            onClick={() => deleteRolePermissionsMutation.mutate(selectedRole?.id)}
            disabled={deleteRolePermissionsMutation.isLoading}
            variant="contained"
            color="error"
          >
            {deleteRolePermissionsMutation.isLoading ? t("common.deleting") : t("common.delete")}
          </Button>
        </DialogActions>
      </Dialog>
      </Box>
    </>
  );
};

export default AssignRolePermissions;
