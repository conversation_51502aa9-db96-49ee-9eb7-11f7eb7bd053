import { useState, useEffect, useMemo } from "react";
import { Add, Edit, Delete } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  IconButton,
  Box,
  Autocomplete,
  Chip,
} from "@mui/material";
import _ from "lodash";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { useLanguageStore } from "../../../store/language";
import { useAuthStore } from "../../../store/auth";
import Table, { ColumnType } from "../../../components/Table";
import { SortDirection } from "../../../data/constants";
import sortHandler from "../../../components/Table/sortHandler";
import Loader from "../../../utils/Loader";
import {
  Contact,
  createContact,
  deleteContact,
  getAllContacts,
  updateContact,
} from "../../../services/contact/contact";
import { getAllRoles, Role } from "../../../services/roles/roles";
import { useLocation, useNavigate } from "react-router-dom";
import { isValidEmail } from "../../../utils";

function PMInternalContactsPage() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { currentLanguage } = useLanguageStore();
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();

  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState<Contact | null>(null);

  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    roles: [] as number[],
  });

  const [formErrors, setFormErrors] = useState({
    first_name: "",
    last_name: "",
    email: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error",
  });

  const [page, setPage] = useState(0);
  const [pageCount, setPageCount] = useState(10);
  const [sort, setSort] = useState<any>({});

  const { data: contactsResponse, isLoading } = useQuery({
    queryKey: ["contacts", page, pageCount, sort],
    queryFn: getAllContacts,
  });

  const contactsData = contactsResponse?.result || [];
  const totalCount = contactsResponse?.totalCount || 0;

  const createMutation = useMutation({
    mutationFn: createContact,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      setSnackbar({
        open: true,
        message: t("pmicontacts.contactCreated"),
        severity: "success",
      });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });

  const updateMutation = useMutation({
    mutationFn: (payload: Contact) => updateContact(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      setSnackbar({
        open: true,
        message: t("pmicontacts.contactUpdated"),
        severity: "success",
      });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setGeneralError(errorMessage);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteContact,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      setSnackbar({
        open: true,
        message: t("pmicontacts.contactDeleted"),
        severity: "success",
      });
      setDeleteDialogOpen(false);
      setContactToDelete(null);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || t("common.error");
      setSnackbar({ open: true, message: errorMessage, severity: "error" });
    },
  });

  const userType = "business";
  const { data: rolesData = [] } = useQuery({
    queryKey: ["roles", userType],
    queryFn: async (): Promise<Role[]> => {
      const response = await getAllRoles();
      const allRoles: Role[] = response.result || [];
      return allRoles.filter((role: Role) => role.userType === userType);
    },
  });

  const defaultColumns: ColumnType[] = useMemo(
    () => [
      {
        title: t("pmicontacts.firstName"),
        key: "first_name",
        searchable: true,
        filterable: true,
        width: "150px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("pmicontacts.lastName"),
        key: "last_name",
        searchable: true,
        filterable: true,
        width: "150px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("pmicontacts.email"),
        key: "email",
        searchable: true,
        filterable: true,
        width: "200px",
        direction: SortDirection.ASC,
        active: false,
      },
      {
        title: t("pmicontacts.roles"),
        key: "roles",
        width: "200px",
        render: (item: Contact) => {
          const roleNames = item?.roles
            ?.map((r: any) => r.role_name)
            .join(", ");

          return (
            <div style={{ whiteSpace: "normal", wordWrap: "break-word" }}>
              {roleNames || "-"}
            </div>
          );
        },
      },
      {
        title: t("common.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        width: "120px",
        render: (item: Contact) => (
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => handleEdit(item)}
              color="primary"
            >
              <Edit fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => handleDeleteClick(item)}
              color="error"
            >
              <Delete fontSize="small" />
            </IconButton>
          </Box>
        ),
      },
    ],
    [t, currentLanguage]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));
  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns,
    });
  };

  const validateForm = () => {
    const errors = { first_name: "", last_name: "", email: "" };
    if (!formData.first_name.trim())
      errors.first_name = t("pmicontacts.firstNameRequired");
    if (!formData.last_name.trim())
      errors.last_name = t("pmicontacts.lastNameRequired");
    if (!formData.email.trim()) errors.email = t("pmicontacts.emailRequired");
    setFormErrors(errors);
    return !Object.values(errors).some((error) => error !== "");
  };

  const handleSubmit = async () => {
    setGeneralError("");
    if (!validateForm()) return;
    setIsSubmitting(true);
    try {
      const payload = {
        first_name: formData.first_name,
        last_name: formData.last_name,
        email: formData.email,
        roles: formData.roles,
      };

      if (editMode && selectedContact) {
        const updatePayload = {
          id: selectedContact.id, // Contact's unique identifier
          first_name: formData.first_name, // First name from the form
          last_name: formData.last_name, // Last name from the form
          email: formData.email, // Email address from the form
          roles: formData.roles, // Array of role IDs (number[])
          updated_by: String(user?.id) || "", // Current user's email from auth store
        };
        updateMutation.mutate(updatePayload);
      } else {
        createMutation.mutate(payload);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (contact: Contact) => {
    setSelectedContact(contact);
    setFormData({
      first_name: contact.first_name,
      last_name: contact.last_name,
      email: contact.email,
      roles: (contact as any)?.roles?.map((r: any) => r.id) || [],
    });
    setEditMode(true);
    setOpen(true);
    setGeneralError("");
    setFormErrors({ first_name: "", last_name: "", email: "" });
  };

  const handleDeleteClick = (contact: Contact) => {
    setContactToDelete(contact);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (contactToDelete) deleteMutation.mutate(contactToDelete.id!);
  };

  const resetForm = () => {
    setFormData({ first_name: "", last_name: "", email: "", roles: [] });
    setFormErrors({ first_name: "", last_name: "", email: "" });
    setGeneralError("");
    setEditMode(false);
    setSelectedContact(null);
  };

  const handleCreateNew = () => {
    resetForm();
    setOpen(true);
  };

  if (isLoading) return <Loader value="Loading..." />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  const openChangeHistory = () => {
    navigate("/pmi-internalcontacts-change-history", {
      state: { backgroundLocation: location },
    });
  };
  return (
    <>
      <Box m={2}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          gap={1}
          mb={2}
        >
          <Typography variant="h4">{t("pmicontacts.title")}</Typography>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleCreateNew}
            >
              {t("pmicontacts.createPmiContact")}
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={openChangeHistory}
            >
              {t("systemPreferences.changeHistory")}
            </Button>
          </Box>
        </Box>

        <Table
          sx={{ height: "auto" }}
          columns={columns}
          data={contactsData || []}
          sortHandler={handleSort}
          pagination={{ totalCount, pageCount, setPageCount, page, setPage }}
          enableSearch
          enableFilter
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />
      </Box>

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {editMode
            ? t("pmicontacts.editContact")
            : t("pmicontacts.createPmiContact")}
        </DialogTitle>
        <DialogContent dividers>
          {generalError && <Alert severity="error">{generalError}</Alert>}

          <TextField
            label={t("pmicontacts.firstName")}
            fullWidth
            margin="dense"
            value={formData.first_name}
            onChange={(e) =>
              setFormData({ ...formData, first_name: e.target.value })
            }
            error={!!formErrors.first_name}
            helperText={formErrors.first_name}
            sx={{ mb: 2 }}
          />
          <TextField
            label={t("pmicontacts.lastName")}
            fullWidth
            margin="dense"
            value={formData.last_name}
            onChange={(e) =>
              setFormData({ ...formData, last_name: e.target.value })
            }
            error={!!formErrors.last_name}
            helperText={formErrors.last_name}
            sx={{ mb: 2 }}
          />
          <TextField
            label={t("pmicontacts.email")}
            fullWidth
            margin="dense"
            disabled={editMode}
            value={formData.email}
            onChange={(e) => {
              const value = e.target.value;
              setFormData({ ...formData, email: value });

              if (!isValidEmail(value)) {
                setFormErrors((prev) => ({
                  ...prev,
                  email: "Invalid email address",
                }));
              } else {
                setFormErrors((prev) => ({ ...prev, email: "" }));
              }
            }}
            error={!!formErrors.email}
            helperText={formErrors.email}
            sx={{ mb: 2 }}
          />
          <Autocomplete
            multiple
            options={rolesData}
            getOptionLabel={(option: Role) => option.role_name}
            value={rolesData.filter((role) =>
              formData.roles.includes(role.id as number)
            )}
            onChange={(event, newValue) => {
              const cleanRoleIds = newValue
                .map((role) => role?.id)
                .filter((id): id is number => typeof id === "number");
              setFormData({ ...formData, roles: cleanRoleIds });
            }}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip label={option.role_name} {...getTagProps({ index })} />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label={t("pmicontacts.roles")}
                placeholder={t("pmicontacts.selectRoles")}
              />
            )}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)} color="inherit">
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting}
          >
            {isSubmitting
              ? t("common.loading")
              : editMode
              ? t("common.update")
              : t("common.create")}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{t("pmicontacts.deleteContact")}</DialogTitle>
        <DialogContent>
          <Typography>{t("pmicontacts.confirmDeleteContact")}</Typography>
          {contactToDelete && (
            <Typography variant="body2" sx={{ mt: 1, fontWeight: "bold" }}>
              {contactToDelete.first_name} {contactToDelete.last_name} (
              {contactToDelete.email})
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} color="inherit">
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending
              ? t("common.loading")
              : t("common.delete")}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <MuiAlert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </MuiAlert>
      </Snackbar>
    </>
  );
}

export default PMInternalContactsPage;
