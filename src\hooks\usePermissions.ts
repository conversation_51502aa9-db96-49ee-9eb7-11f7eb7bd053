import { useAuthStore } from '../store/auth';

export const usePermissions = () => {
  const { user } = useAuthStore();

  // Get all user permissions from roles
  const getUserPermissions = (): string[] => {
    const userPermissions: string[] = [];
    
    if (user?.roles && Array.isArray(user.roles)) {
      user.roles.forEach((role: any) => {
        if (role.permissions && Array.isArray(role.permissions)) {
          role.permissions.forEach((permission: any) => {
            userPermissions.push(permission.permission_name || permission.name);
          });
        }
      });
    }

    // If user has direct permissions (fallback)
    if (user?.permissions && Array.isArray(user.permissions)) {
      user.permissions.forEach((permission: any) => {
        userPermissions.push(permission.permission_name || permission.name);
      });
    }

    return [...new Set(userPermissions)]; // Remove duplicates
  };

  // Get all user roles
  const getUserRoles = (): string[] => {
    const userRoles: string[] = [];
    
    if (user?.roles && Array.isArray(user.roles)) {
      user.roles.forEach((role: any) => {
        userRoles.push(role.role_name || role.name);
      });
    }

    // If user has a single role (fallback)
    if (user?.role) {
      userRoles.push(user.role);
    }

    return [...new Set(userRoles)]; // Remove duplicates
  };

  // Check if user has specific permission
  const hasPermission = (permission: string): boolean => {
    if (user?.role === 'admin') return true;
    return getUserPermissions().includes(permission);
  };

  // Check if user has any of the specified permissions
  const hasAnyPermission = (permissions: string[]): boolean => {
    if (user?.role === 'admin') return true;
    const userPermissions = getUserPermissions();
    return permissions.some(permission => userPermissions.includes(permission));
  };

  // Check if user has all of the specified permissions
  const hasAllPermissions = (permissions: string[]): boolean => {
    if (user?.role === 'admin') return true;
    const userPermissions = getUserPermissions();
    return permissions.every(permission => userPermissions.includes(permission));
  };

  // Check if user has specific role
  const hasRole = (role: string): boolean => {
    return getUserRoles().includes(role);
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles: string[]): boolean => {
    const userRoles = getUserRoles();
    return roles.some(role => userRoles.includes(role));
  };

  // Check if user has all of the specified roles
  const hasAllRoles = (roles: string[]): boolean => {
    const userRoles = getUserRoles();
    return roles.every(role => userRoles.includes(role));
  };

  return {
    user,
    getUserPermissions,
    getUserRoles,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
  };
};
