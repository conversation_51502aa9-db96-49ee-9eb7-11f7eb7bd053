import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  MenuItem,
  TextField,
  Typography,
  Paper,
  IconButton,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import api from "../../services/httpClient/api";
import DeleteIcon from "@mui/icons-material/Delete";
import { useSnackbar } from "../../utils/SnackbarProvider";
import VendorStepper from "../../components/steppedbar/VendorStepper";
import { useAuthStore } from "../../store/auth";

interface Location {
  id: number;
  address_line1: string;
  address_line2: string;
  street: string;
  zip: string;
  city: string;
  country: string;
  location_type: "production" | "pickup" | "";
}

const VendorLocationsView: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { showMessage } = useSnackbar();
const { user } = useAuthStore();
const vendorId = Number(user?.id) || 0;
  const [locations, setLocations] = useState<Location[]>([]);
  const [hasExistingLocations, setHasExistingLocations] = useState(false);
  const [currentStep, setCurrentStep] = useState(2);

useEffect(() => {
  const fetchLocations = async () => {
    try {
      const response = await api.get(`/user/vendor-location/vendor/${vendorId}`);

      // Check if response has the expected structure with data field
      if (response.data?.success && response.data?.data) {
        const locationData = response.data.data;
        // Wrap single location object in array since form expects array
        setLocations([locationData]);
        setHasExistingLocations(true);
      } else {
        // Initialize with empty location for new entry
        setLocations([{
          id: 0,
          address_line1: "",
          address_line2: "",
          street: "",
          zip: "",
          city: "",
          country: "",
          location_type: "",
        }]);
        setHasExistingLocations(false);
      }
    } catch (error) {
      console.error("Error fetching vendor locations:", error);
      // Initialize with empty location on error
      setLocations([{
        id: 0,
        address_line1: "",
        address_line2: "",
        street: "",
        zip: "",
        city: "",
        country: "",
        location_type: "",
      }]);
      setHasExistingLocations(false);
    }
  };

  fetchLocations();
}, [vendorId]);

console.log("Fetched locations:", locations, "Has existing:", hasExistingLocations);

  const handleLocationChange = (
    index: number,
    field: keyof Location,
    value: string
  ) => {
    const updated:any = [...locations];
    updated[index][field] = value;
    setLocations(updated);
  };

  const handleAddLocation = () => {
    setLocations([
      ...locations,
      {
        id: 0,
        address_line1: "",
        address_line2: "",
        street: "",
        zip: "",
        city: "",
        country: "",
        location_type: "",
      },
    ]);
  };

  const handleDeleteLocation = async (index: number, locationId: number) => {
    if (locationId !== 0) {
      try {
        await api.delete(`/user/vendor-location/${locationId}`);
        showMessage("Location deleted successfully.", "success");
      } catch (error) {
        console.error("Failed to delete location", error);
        showMessage("Failed to delete location.", "error");
        return;
      }
    }
    setLocations(locations.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    try {
      if (hasExistingLocations) {
        // Update existing locations
        const updatePayload = {
          updates: locations.map(location => ({
            id: location.id,
            address_line1: location.address_line1,
            address_line2: location.address_line2,
            street: location.street,
            zip: location.zip,
            city: location.city,
            country: location.country,
            location_type: location.location_type,
          })),
        };

        const response = await api.post("/user/vendor-location", updatePayload);

        if (response?.data?.success) {
          showMessage("Locations updated successfully!", "success");
          navigate("/vendor-onboarding/location-details");
        } else {
          showMessage(response?.data?.message || "Update failed.", "error");
        }
      } else {
        // Create new locations
        const createPayload = locations.map((location, index) => ({
            id: 0, // Start from 0
            address_line1: location.address_line1,
            address_line2: location.address_line2,
            street: location.street,
            zip: location.zip,
            city: location.city,
            country: location.country,
            location_type: location.location_type, // e.g., 'production'
          }));

        const response = await api.post("/user/vendor-location", createPayload);

        if (response?.data?.success) {
          showMessage("Locations saved successfully!", "success");
          navigate("/dashboard"); // Navigate to dashboard since next step doesn't exist yet
        } else {
          showMessage(response?.data?.message || "Save failed.", "error");
        }
      }
    } catch (error: any) {
      console.error("Save failed", error);
      showMessage(
        error?.response?.data?.message || "An unexpected error occurred.",
        "error"
      );
    }
  };

  return (
    <>
      <VendorStepper activeStep={currentStep} />
      <Box p={3}>
        <Typography variant="h6" gutterBottom>
          Vendor Locations
        </Typography>

    
          {locations.map((loc, index) => (
            <Paper
              key={index}
              sx={{
                mb: 2,
                p: 3,
                borderRadius: 2,
                position: "relative",
                backgroundColor: "#f9f9f9",
              }}
            >
              <IconButton
                onClick={() => handleDeleteLocation(index, loc.id)}
                sx={{ position: "absolute", top: 8, right: 8 }}
              >
                <DeleteIcon color="error" />
              </IconButton>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Address Line 1"
                    value={loc.address_line1}
                    onChange={(e) =>
                      handleLocationChange(
                        index,
                        "address_line1",
                        e.target.value
                      )
                    }
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Address Line 2"
                    value={loc.address_line2}
                    onChange={(e) =>
                      handleLocationChange(
                        index,
                        "address_line2",
                        e.target.value
                      )
                    }
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Street"
                    value={loc.street}
                    onChange={(e) =>
                      handleLocationChange(index, "street", e.target.value)
                    }
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="ZIP Code"
                    value={loc.zip}
                    onChange={(e) =>
                      handleLocationChange(index, "zip", e.target.value)
                    }
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="City"
                    value={loc.city}
                    onChange={(e) =>
                      handleLocationChange(index, "city", e.target.value)
                    }
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Country"
                    value={loc.country}
                    onChange={(e) =>
                      handleLocationChange(index, "country", e.target.value)
                    }
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Location Type"
                    select
                    value={loc.location_type}
                    onChange={(e) =>
                      handleLocationChange(
                        index,
                        "location_type",
                        e.target.value as "production" | "pickup"
                      )
                    }
                    fullWidth
                    required
                  >
                    <MenuItem value="production">Production</MenuItem>
                    <MenuItem value="pickup">Pickup</MenuItem>
                  </TextField>
                </Grid>
              </Grid>
            </Paper>
          ))}

        <Box display="flex" justifyContent="flex-end" mb={2}>
          <Button variant="outlined" onClick={handleAddLocation}>
            Add Location
          </Button>
        </Box>

        <Box display="flex" justifyContent="space-between" mt={3}>
          <Button
            variant="contained"
            onClick={() => {
              navigate("/vendor-onboarding/contact-details");
            }}
          >
            Back
          </Button>

          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={locations.length === 0}
          >
            {hasExistingLocations ? "Update & Next" : "Save & Next"}
          </Button>
        </Box>
      </Box>
    </>
  );
};

export default VendorLocationsView;
