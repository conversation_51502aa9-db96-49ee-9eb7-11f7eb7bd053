import React from "react";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  LinearProgress,
  IconButton,
} from "@mui/material";
import { Box } from "@mui/system";
import _ from "lodash";
import { useEffect, useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import { getAllvendors, InviteVendor } from "../../services/vendor/vendor";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import { vendorMenu } from "../../data/constants";
import { useNavigate } from "react-router-dom";
import useQueryParams from "../../hooks/useQueryParams";
import { StyledProfileNav, StyledProfileNavItem } from "./styles";
import NoData from "./Nodata";
import SampleData2 from "./sampleData2";
import SampleData3 from "./sampleData3";  
import MapIcon from '@mui/icons-material/Map';
import VisibilityIcon from "@mui/icons-material/Visibility";
import InfoIcon from "@mui/icons-material/Info";
import Loader from "../../utils/Loader";
import { formatStatus } from "../../utils";
import { formatDate } from "../../utils/dateFormats";
import { getAllGlobalSystemPreferences } from "../../services/preferences/preferences";
import { ViewallUsers } from "../../services/auth/authService";
import VendorCompanyProfilePage from "../vendor/CompanyProfileDetails";

function UsersPage() {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(0);
  const [pageCount, setPageCount] = useState<number>(10);
  const [sort, setSort] = useState<any>({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const { user } = useAuthStore();
  const { queryParams } = useQueryParams();
  const active: any = queryParams.tab;
  const [contactDialogOpen, setContactDialogOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);

  useEffect(() => {
    if (!active) {
      navigate("?tab=vendor-information", { replace: true });
    }
  }, [active, navigate]);

  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });
  const { data: usersData = [] } = useQuery({
    queryKey: ["ViewallUsers", page, pageCount, sort],
    queryFn: async () => {
      const response = await ViewallUsers();
      const allUsers = response.result || [];

      // Filter users based on current tab's userType
      return allUsers;
    },
  });
  const businessUsers = usersData.filter((user: any) => user.userType === "business");
  const vendorUsers = usersData.filter((user: any) => user.userType === "vendor");

  const defaultColumns: Array<ColumnType> = useMemo(
    () => [
      {
        title: t("users.firstName"),
        key: "first_name",
        sort: true,
        translatable: true,
        searchable: true,
        filterable: true,
        width: "150px"
      },
      {
        title: t("users.lastName"),
        key: "last_name",
        sort: true,
        searchable: true,
        filterable: true,
        width: "150px"
      },
      {
        title: t("users.email"),
        key: "email",
        sort: true,
        searchable: true,
        filterable: true,
        width: "200px"
      },
      {
        title: t("users.status"),
        key: "invite_status",
        sort: true,
        filterable: true,
        width: "120px",
        render: (row: any) => formatStatus(row.invite_status),
      },
      {
        title: t("vendor.registrationDate"),
        key: "createdAt",
        sort: true,
        filterable: true,
        width: "150px",
        render: (row) => {
          const dateFormat = preferences?.result[0]?.date_format || "YYYY-MM-DD";
          return <span>{formatDate(row.createdAt, dateFormat)}</span>;
        },
      },
      {
        title: t("vendor.contractEndDate"),
        key: "contractEndDate",
        searchable: false,
        filterable: false
      },
      {
        title: t("vendor.contactInformation"),
        key: "contactInformation",
        searchable: false,
        filterable: false
      },
        {
        title: t("vendor.country"),
        key: "Country",
        searchable: false,
        filterable: false
      },
      {
        title: t("common.processing"),
        key: "processing",
        searchable: false,
        filterable: false,
        render: () => (
          <Box sx={{ width: "100%" }}>
            <Typography variant="body2" color="text.secondary">
              50%
            </Typography>
            <LinearProgress
              variant="determinate"
              value={25}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
        ),
      },
      {
        title: t("common.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        render: (row:any) => (
          <IconButton
            size="small"
            onClick={() => {
              setSelectedRow(row);
              setContactDialogOpen(true);
            }}
            aria-label="View details"
          >
            <VisibilityIcon />
          </IconButton>
        ),
      },
      {
        title: t("vendor.vendorDetails"),
        key: "vendorDetails",
        searchable: false,
        filterable: false,
        render: (row: any) => (
          <IconButton
            onClick={() => navigate(`/companyprofile-details/${row.id}`)}
            aria-label="Details"
          >
            <InfoIcon />
          </IconButton>
        ),
      },
      {
        title: t("vendor.pmContacts"),
        key: "pmContacts",
        searchable: false,
        filterable: false,
        render: (row: any) => (
          <IconButton
            onClick={() => navigate(`/pmi-contacts/${row.id}`)}
            aria-label="Details"
          >
            <MapIcon  />
          </IconButton>
        ),
      },
    ],
    [t, currentLanguage, preferences]
  ); // Dependencies for useMemo

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  // Update columns when language changes
  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
  });

  const [formErrors, setFormErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: existingPreferences, isLoading } = useQuery({
    queryKey: ["getAllvendors", page, pageCount, sort],
    queryFn: async () => {
      const response = await getAllvendors();
      return response.result;
    },
  });

  const totalCount = existingPreferences?.length || 0;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: "",
      });
    }

    if (generalError) {
      setGeneralError("");
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formData.firstName.trim()) {
      newErrors.firstName = t("forms.validation.required");
      valid = false;
    } else if (formData.firstName.length < 2) {
      newErrors.firstName = t("forms.validation.minLength", { min: 2 });
      valid = false;
    } else {
      newErrors.firstName = "";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = t("forms.validation.required");
      valid = false;
    } else {
      newErrors.lastName = "";
    }

    if (!formData.email.trim()) {
      newErrors.email = t("forms.validation.required");
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t("forms.validation.email");
      valid = false;
    } else {
      newErrors.email = "";
    }

    setFormErrors(newErrors);
    return valid;
  };

  const handleSubmit = async () => {
    setGeneralError("");

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const payload = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
      };

      const response = await InviteVendor(payload);
      console.log("Vendor invited:", response);

      if (response?.data?.success === false || response?.success === false) {
        const apiMessage = response?.data?.message || response?.message;
        let errorMessage = t("common.unknownError");
        if (
          apiMessage?.includes("invited already") ||
          apiMessage?.includes("already invited")
        ) {
          errorMessage = t("vendor.vendorAlreadyInvited");
        } else if (apiMessage) {
          errorMessage = apiMessage; 
        }

        setGeneralError(errorMessage);
        return;
      }
      setOpen(false);
      setSnackbarOpen(true);
      setFormData({ firstName: "", lastName: "", email: "" });
      setGeneralError(""); 
    } catch (error: any) {
      console.error("Error inviting vendor:", error);

      if (error.response?.data?.success === false) {
        const apiMessage = error.response.data.message;
        let errorMessage = t("common.unknownError");
        if (
          apiMessage?.includes("invited already") ||
          apiMessage?.includes("already invited")
        ) {
          errorMessage = t("vendor.vendorAlreadyInvited");
        } else if (apiMessage) {
          errorMessage = apiMessage;
        }

        setGeneralError(errorMessage);
      } else if (error.response?.data?.message) {
        // Other API error messages
        const apiMessage = error.response.data.message;
        let errorMessage = t("common.unknownError");

        if (
          apiMessage?.includes("invited already") ||
          apiMessage?.includes("already invited")
        ) {
          errorMessage = t("vendor.vendorAlreadyInvited");
        } else if (apiMessage) {
          errorMessage = apiMessage;
        }

        setGeneralError(errorMessage);
      } else if (error.message) {
        // Network or other errors
        setGeneralError(error.message);
      } else {
        setGeneralError(t("common.unknownError"));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) return <Loader value={"Loading..."} />;
const typeOfUser = user?.role || "";
  if (typeOfUser === "business") {
    return (
      <Box p={3} pb={0} px={1}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mt={2}
        >
          <Typography variant="subtitle1" color="primary">
            {t("vendor.title")}
          </Typography>
          <Box mb={2}>
          <Button
            onClick={() => {
              setOpen(true);
              setGeneralError(""); // Clear any previous errors when opening dialog
              setFormErrors({ firstName: "", lastName: "", email: "" }); // Clear field errors
            }}
            variant="contained"
            startIcon={<Add />}
            color="secondary"
          >
            {t("vendor.inviteVendor")}
          </Button>
          </Box>
          <Dialog
            open={open}
            onClose={() => setOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>{t("vendor.inviteVendor")}</DialogTitle>
            <DialogContent>
              {generalError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {generalError}
                </Alert>
              )}
              {formErrors.firstName && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {formErrors.firstName}
                </Alert>
              )}
              {formErrors.lastName && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {formErrors.lastName}
                </Alert>
              )}
              {formErrors.email && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {formErrors.email}
                </Alert>
              )}

              <TextField
                margin="dense"
                label={t("users.firstName")}
                name="firstName"
                fullWidth
                variant="outlined"
                value={formData.firstName}
                onChange={handleChange}
                error={!!formErrors.firstName}
                helperText={formErrors.firstName}
                disabled={isSubmitting}
                placeholder={t("forms.placeholders.enterName")}
              />
              <TextField
                margin="dense"
                label={t("users.lastName")}
                name="lastName"
                fullWidth
                variant="outlined"
                value={formData.lastName}
                onChange={handleChange}
                error={!!formErrors.lastName}
                helperText={formErrors.lastName}
                disabled={isSubmitting}
                placeholder={t("forms.placeholders.enterName")}
              />
              <TextField
                margin="dense"
                label={t("users.email")}
                name="email"
                fullWidth
                variant="outlined"
                value={formData.email}
                onChange={handleChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                disabled={isSubmitting}
                placeholder={t("forms.placeholders.enterEmail")}
              />
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setOpen(false)}
                color="primary"
                disabled={isSubmitting}
              >
                {t("common.cancel")}
              </Button>
              <Button
                onClick={handleSubmit}
                color="primary"
                variant="contained"
                disabled={isSubmitting}
              >
                {isSubmitting ? t("common.processing") : t("common.submit")}
              </Button>
            </DialogActions>
          </Dialog>
        </Box>

        <Snackbar
          open={snackbarOpen}
          autoHideDuration={4000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <MuiAlert
            onClose={() => setSnackbarOpen(false)}
            severity="success"
            sx={{ width: "100%" }}
          >
            {t("vendor.vendorInvited")}
          </MuiAlert>
        </Snackbar>

        <Box>
          {existingPreferences && existingPreferences.length > 0 ? (
            <Table
               sx={{height:'auto'}}
              pagination={{
                totalCount,
                pageCount,
                setPageCount,
                page,
                setPage,
              }}
              data={existingPreferences}
              columns={columns}
              loading={isLoading}
              sortHandler={handleSort}
              enableSearch={true}
              enableFilter={true}
              searchPlaceholder={t("table.searchPlaceholder")}
            />
          ) : (
            <Box textAlign="center" mt={20}>
              <Typography variant="subtitle1" color="rgba(0,0,0,0.5)">
                {t("vendor.noDataAvailable")}
              </Typography>
            </Box>
          )}
        </Box>

        <Dialog
          open={contactDialogOpen}
          onClose={() => setContactDialogOpen(false)}
          maxWidth="xs"
          fullWidth
        >
          <DialogTitle>{t("vendor.vendorDetails")}</DialogTitle>
          <DialogContent>
            {selectedRow ? (
              <Typography>{t("vendor.noDataAvailable")}</Typography>
            ) : (
              <Typography>{t("vendor.noDataAvailable")}</Typography>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setContactDialogOpen(false)} color="primary">
              {t("common.close")}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  }

  if (typeOfUser === "vendor") {
    return (
      <>
        <Box sx={{ position: "relative", bgcolor: "white", zIndex: 2 }}>
          <StyledProfileNav>
            {vendorMenu.map((item, index) => (
              <StyledProfileNavItem
                key={index}
                onClick={() => navigate(`?tab=${item.path}`)}
                active={active === item.path ? 1 : 0}
              >
                {t(item.titleKey)}
              </StyledProfileNavItem>
            ))}
          </StyledProfileNav>
        </Box>

        <Box px={2} pl={1} pr={1} pt={3}>
          {/* {active === "vendor-information" && <VendorCompanyProfilePage />} */}
          {active === "no-data" && <NoData />}
          {active === "sample-data-2" && <SampleData2 />}
          {active === "sample-data-3" && <SampleData3 />}
        </Box>
      </>
    );
  }

  return null;
}

export default UsersPage;
