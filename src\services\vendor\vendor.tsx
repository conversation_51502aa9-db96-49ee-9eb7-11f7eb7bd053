import { User } from "../../types";
import api from "../httpClient/api";


// Get all users (with pagination)
export const getAllvendors = async (

) => {
  const response = await api.get('/user/view-vendors');
  return response.data;
};


// Create new Vendor
export const InviteVendor = async (userData: Partial<User>) => {
  const response = await api.post('/user/invite-vendor', userData);
  return response.data;
};

// Vendor signin
export const vendorSignIn = async (userData: Partial<User>) => {
  const response = await api.post('/user/vendor-signin', userData);
  console.log(response,"response InviteVendor");
  return response.data;
};
// Update Vendor
export const updateUser = async (id: string, userData: Partial<User>) => {
  const response = await api.patch(`/users/${id}`, userData);
  return response.data;
};

// Delete Vendor
export const deleteUser = async (id: string) => {
  await api.delete(`/users/${id}`);
  return true;
};

export const uploadCsvToAzure = async (formData: FormData) => {
  const response = await api.post("/api/vendor/upload-csv", formData);
  return response.data;
};
