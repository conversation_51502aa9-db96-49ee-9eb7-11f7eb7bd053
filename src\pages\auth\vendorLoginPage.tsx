import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Navigate } from "react-router-dom";
import { Box, Typography,Paper } from "@mui/material";
import { useAuthStore } from "../../store/auth";
import LanguageSwitcher from "../../components/layout/LanguageSwitcher";
import { clearPartialAuthState } from "../../utils/authUtils";
import { useReactiveLogo } from "../../hooks/useSystemPreferences";
import VendorLoginForm from "../../components/auth/vendorLoginForm";

const VendorLoginPage: React.FC = () => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuthStore();
  const reactiveLogo = useReactiveLogo();
  const BASE_URL = import.meta.env.VITE_ASSETS_BASE_URL || '';

  useEffect(() => {
    clearPartialAuthState();
  }, []);

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        backgroundColor: "#f3f4ff",
      }}
    >
      {/* Left Panel */}
      <Box
        sx={{
          width: "50%",
          backgroundColor: "#778895",
          color: "#fff",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          px: 4,
        }}
      >
        <Box
          component="img"
          src={reactiveLogo}
          alt="Logo"
          sx={{
            width: "100%",
            maxWidth: 400,
            objectFit: 'contain'
          }}
          onError={(e) => {
            console.error('Logo failed to load:', reactiveLogo);
            // Fallback to default logo
            const target = e.target as HTMLImageElement;
            target.src = `${BASE_URL}logo.png`;
          }}
        />
        <Typography variant="h5" sx={{ mt: 2, textAlign: "center" }}>
          Welcome to Vendor Portal ! Please login to your account.
        </Typography>
      </Box>

      {/* Right Panel */}
      <Box
        sx={{
          width: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper elevation={6} sx={{ width: "80%", maxWidth: 400, p: 4 }}>
          <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
            <LanguageSwitcher />
          </Box>

          <Typography variant="h5" gutterBottom>
            {t("auth.vendorLoginTitle") || "Login"}
          </Typography>

          <VendorLoginForm />
        </Paper>
      </Box>
    </Box>
  );
};

export default VendorLoginPage;
