{"common": {"refreshRates": "Refresh rates", "departments": "Departments", "loading": "Loading...", "error": "An error occurred", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "back": "Back", "next": "Next", "logout": "Logout", "profile": "Profile", "settings": "Settings", "changePassword": "Change Password", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "yes": "Yes", "no": "No", "confirm": "Confirm", "close": "Close", "submit": "Submit", "reset": "Reset", "clear": "Clear", "refresh": "Refresh", "update": "Update", "create": "Create", "add": "Add", "remove": "Remove", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "noData": "No data available", "noResults": "No results found", "success": "Success", "warning": "Warning", "createdAt": "Created At", "info": "Information", "required": "Required", "optional": "Optional", "pleaseWait": "Please wait...", "processing": "Processing...", "saving": "Saving...", "updating": "Updating...", "deleting": "Deleting...", "uploading": "Uploading...", "downloading": "Downloading...", "validationError": "Validation Error", "networkError": "Network Error", "serverError": "Server Error", "unknownError": "Unknown Error", "accessDenied": "Access Denied", "insufficientPermissions": "You don't have sufficient permissions to access this resource"}, "auth": {"changePasswordFor": "Change password for", "oldPassword": "Old Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "credentialsChanged": "Credentials changed successfully", "enterVerificationCodeSentToMail": "Please enter the code sent to your mail", "requiredOldPassword": "Old password is required", "requiredNewPassword": "New password is required", "passwordMismatch": "Passwords do not match", "requiredVerificationCode": "Verification code is required", "submit": "Submit", "passwordUpdatedCheckEmail": "Old password verified. Please check your mail for verification code.", "errorChangingPassword": "Error changing password.", "invalidVerificationCode": "Invalid verification code.", "passwordChangeSuccess": "Password changed successfully!", "title": "<PERSON><PERSON>", "Oldpassword": "Old Password", "changePassword": "Change Password", "vendorLoginTitle": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON>", "email": "Email", "password": "Password", "resetPasswordTitle": "Reset Password", "confirmPassword": "Confirm Password", "resetPassword": "Reset Password", "mailTemplateUpdated": "Mail notification template updated successfully", "resettingPasswordFor": "Resetting password for", "resetPasswordDescription": "Please enter your new password", "credentialsChangedSuccess": "Password changed successfully!", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "emailRequired": "Email is required", "invalidEmail": "Please enter a valid email", "nameRequired": "Name is required", "passwordRequired": "Password is required", "invalidCredentials": "Invalid email or password", "accountLocked": "Your account has been locked. Please contact support.", "accountDisabled": "Your account has been disabled.", "loginSuccess": "Login successful", "twoFactorTitle": "Please enter security code", "twoFactorDescription": "Please enter the verification code sent to your email", "EnterOldPassword": "Enter your old password For :", "verificationCode": "Verification code", "verifyCode": "Verify Code", "codeRequired": "Verification code is required", "invalidCode": "Invalid verification code", "backToLogin": "Back to login", "forgotPasswordTitle": "Forgot Password", "forgotPasswordDescription": "Enter your email address to reset your password", "sendResetLink": "Send Reset Link", "resetLinkSent": "Reset link sent to your email", "sessionExpired": "Your session has expired. Please login again", "verificationSuccessful": "Verification successful", "verificationFailed": "Verification failed", "verificationCodeSent": "A verification code has been sent to your email", "preferencesUpdated": "Preferences updated", "mailTemplatedAdded": "Mail Template added", "userInvited": "<PERSON><PERSON><PERSON> invited successfully", "invalidStatus": "Invalid status", "somethingWentWrong": "Something went wrong", "invalidVerification": "Invalid verification", "accessDenied": "Access denied. Please login first", "redirecting": "Redirecting...", "passwordResetSuccess": "Password reset successful", "redirectingToLogin": "Redirecting to login page...", "passwordTooShort": "Password must be at least 8 characters long", "passwordsDoNotMatch": "Passwords do not match", "confirmPasswordRequired": "Please confirm your password", "newPasswordRequired": "New password is required", "currentPasswordRequired": "Current password is required", "invalidPasswordFormat": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "emailNotFound": "Email address not found", "tokenExpired": "Reset token has expired", "invalidToken": "Invalid reset token", "tooManyAttempts": "Too many login attempts. Please try again later.", "accountNotVerified": "Account not verified. Please check your email.", "loginRequired": "Please login to continue", "permissionDenied": "Permission denied", "sessionTimeout": "Session timeout", "logoutSuccess": "Logged out successfully", "loginFailed": "<PERSON><PERSON> failed", "registrationSuccess": "Registration successful", "registrationFailed": "Registration failed", "emailAlreadyExists": "Email address already exists", "weakPassword": "Password is too weak", "strongPassword": "Strong password", "mediumPassword": "Medium strength password"}, "navigation": {"dashboard": "Dashboard", "PMIInternalContacts": "PMI Internal Contacts", "vendor": "Vendor Management", "assaignUserRoles": "Assign User Roles", "assignRolePermissions": "Assign Role Permissions", "roleAuditHistory": "Role Audit History", "roles": "Roles", "permissions": "Permissions", "rolesManagement": "Roles Management", "masterDataManagement": "Master Data Management", "vendorManagement": "Vendor Management", "uom": "Units of Measurement", "systemPreferences": "System Preferences", "notificationPreferences": "Notification Templates"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Dashboard", "stats": "Statistics", "recentActivity": "Recent Activity", "todayStats": "Today's Statistics", "totalUsers": "Total Users", "activeUsers": "Active Users", "newUsers": "New Users", "revenue": "Revenue"}, "users": {"title": "Users", "welcome": "Welcome to Users", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "role": "Role", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "admin": "Admin", "user": "User", "confirmDelete": "Are you sure you want to delete this user?"}, "vendor": {"title": "Vendor Management", "welcome": "Welcome to Vendor Management", "addVendor": "Add <PERSON>", "editVendor": "<PERSON>", "deleteVendor": "Delete Vendor", "inviteVendor": "<PERSON><PERSON><PERSON>", "vendorList": "Vendor List", "vendorDetails": "<PERSON><PERSON><PERSON>", "pmContacts": "PMI Contacts", "basicInformation": "Basic Information", "contactInformation": "Contact Information", "country": "Country", "companyName": "Company Name", "vendorName": "Vendor Name", "vendorEmail": "<PERSON><PERSON><PERSON>", "phoneNumber": "Phone Number", "address": "Address", "city": "City", "state": "State", "zipCode": "Zip Code", "website": "Website", "taxId": "Tax ID", "businessLicense": "Business License", "vendorType": "Vendor Type", "serviceCategory": "Service Category", "contractStartDate": "Contract Start Date", "contractEndDate": "Contract End Date", "paymentTerms": "Payment Terms", "creditLimit": "Credit Limit", "vendorRating": "<PERSON><PERSON><PERSON>", "lastActivity": "Last Activity", "registrationDate": "Registration Date", "approvalStatus": "Approval Status", "approved": "Approved", "pending": "Pending", "rejected": "Rejected", "suspended": "Suspended", "confirmDeleteVendor": "Are you sure you want to delete this vendor?", "vendorCreated": "<PERSON><PERSON><PERSON> created successfully", "vendorUpdated": "Vendor updated successfully", "vendorDeleted": "<PERSON><PERSON><PERSON> deleted successfully", "vendorInvited": "<PERSON><PERSON><PERSON> invited successfully", "vendorApproved": "Vendor approved successfully", "vendorRejected": "<PERSON><PERSON><PERSON> rejected successfully", "vendorSuspended": "<PERSON><PERSON><PERSON> suspended successfully", "vendorActivated": "Vendor activated successfully", "vendorAlreadyInvited": "The vendor has been invited already", "vendorInvitationFailed": "Failed to invite vendor", "section1": "Section 1", "section2": "Section 2", "section3": "Section 3", "noDataAvailable": "No data available"}, "notification": {"title": "Notification Templates", "welcome": "Welcome to Notification Templates", "addTemplate": "Add Template", "editTemplate": "Edit Template", "deleteTemplate": "Delete Template", "notificationTitle": "Notification Title", "subject": "Subject", "messageBody": "Message Body", "actions": "Actions", "preview": "Preview", "back": "Back", "titleLabel": "Notification Title *", "emailTemplates": "Email Templates", "saveTemplate": "Save Template", "templateCode": "Template Code", "templateName": "Template Name", "templateType": "Template Type", "lastModified": "Last Modified", "createdBy": "Created By", "templateStatus": "Template Status", "emailBody": "Email Body", "templateCreated": "Template created successfully", "templateUpdated": "Template updated successfully", "templateDeleted": "Template deleted successfully", "confirmDeleteTemplate": "Are you sure you want to delete this template?", "templateRequired": "Template is required", "subjectRequired": "Subject is required", "messageBodyRequired": "Message body is required", "titleRequired": "Title is required", "invalidTemplateFormat": "Invalid template format", "templateSaved": "Template saved successfully", "savingTemplate": "Saving template...", "updatingTemplate": "Updating template..."}, "table": {"rowsPerPage": "Rows per page:", "of": "of", "page": "Page", "nextPage": "Next page", "previousPage": "Previous page", "firstPage": "First page", "lastPage": "Last page", "noDataToDisplay": "No data to display", "loading": "Loading data...", "selectAllRows": "Select all rows", "selectRow": "Select row", "sortBy": "Sort by", "filterBy": "Filter by", "searchPlaceholder": "Search...", "clearFilters": "Clear filters", "exportData": "Export data", "importData": "Import data", "refreshData": "Refresh data", "totalRecords": "Total records", "selectedRecords": "Selected records", "showingRecords": "Showing {{from}} to {{to}} of {{total}} records", "recordsPerPage": "Records per page", "goToPage": "Go to page", "sortAscending": "Sort ascending", "sortDescending": "Sort descending", "columnSettings": "Column settings", "hideColumn": "Hide column", "showColumn": "Show column", "resizeColumn": "Resize column", "freezeColumn": "Freeze column", "unfreezeColumn": "Unfreeze column", "all": "All", "filter": "Filter", "noMatchingResults": "No matching results found"}, "forms": {"validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum {{min}} characters required", "maxLength": "Maximum {{max}} characters allowed", "pattern": "Invalid format", "numeric": "Please enter a valid number", "positiveNumber": "Please enter a positive number", "integer": "Please enter a whole number", "decimal": "Please enter a valid decimal number", "url": "Please enter a valid URL", "phone": "Please enter a valid phone number", "date": "Please enter a valid date", "time": "Please enter a valid time", "dateRange": "End date must be after start date", "fileSize": "File size must be less than {{size}}MB", "fileType": "Invalid file type. Allowed types: {{types}}", "passwordStrength": "Password must contain at least 8 characters with uppercase, lowercase, and numbers", "confirmPassword": "Passwords must match", "uniqueValue": "This value already exists", "invalidSelection": "Please make a valid selection", "minimumSelection": "Please select at least {{min}} items", "maximumSelection": "You can select maximum {{max}} items"}, "placeholders": {"enterEmail": "Enter your email address", "enterPassword": "Enter your password", "enterName": "Enter your name", "enterPhone": "Enter phone number", "enterAddress": "Enter address", "selectOption": "Select an option", "selectDate": "Select date", "selectTime": "Select time", "uploadFile": "Upload file", "searchHere": "Search here...", "typeHere": "Type here...", "chooseFile": "Choose file", "dragDropFile": "Drag and drop file here"}}, "systemPreferences": {"title": "System Preferences", "pmiContact": "PMI Contact", "oldValue": "Old Value", "newValue": "New Value", "changedBy": "Changed By", "changedDate": "Changed Date", "action": "Action", "date": "Date", "user": "User", "preference": "Preference", "columnName": "Column Name", "changeHistory": "Change History", "changeHistorySystem": "System Preferences - Change History", "vendorProfilechangeHistory": "Vendor Company Profile - Change History", "pmiContactsChangeHistory": "PMI Internal Contacts - Change History", "welcome": "Welcome to System Preferences", "dateFormatLabel": "Date Format", "defaultLanguage": "Default Language", "defaultTimeZone": "Default Time Zone", "globalSettings": "Global Settings", "emailSettings": "<PERSON><PERSON>s", "emailSignature": "Email Signature", "brandingMaterial": "Branding Material", "save": "Save", "delete": "Delete", "defaultFromEmail": "Default From Email", "noReplyEmail": "No Reply Email", "supportEmail": "Support Email", "adminEmail": "<PERSON><PERSON>", "deletePreferences": "Delete Preferences", "savePreferences": "Save Preferences", "uploadLogo": "Upload Logo", "uploadFavicon": "Upload Favicon", "logoUploaded": "Logo uploaded successfully", "faviconUploaded": "Favicon uploaded successfully", "logoRemoved": "Logo removed successfully", "faviconRemoved": "Favicon removed successfully", "invalidLogoFormat": "Invalid logo format. Please upload PNG, JPG, or SVG files.", "invalidFaviconFormat": "Invalid favicon format. Please upload ICO, PNG, or SVG files.", "logoSizeLimit": "Logo size must be less than 5MB", "faviconSizeLimit": "Favicon size must be less than 2MB", "noLogoSelected": "No logo file selected", "noFaviconSelected": "No favicon file selected", "logoPreview": "Logo Preview", "faviconPreview": "Favicon Preview", "uploadNow": "Upload Now", "uploading": "Uploading...", "uploadingLogo": "Uploading Logo...", "uploadingFavicon": "Uploading Favicon...", "emailSignaturePlaceholder": "Enter your email signature here", "defaultFromEmailPlaceholder": "e.g., <EMAIL>", "noReplyEmailPlaceholder": "e.g., <EMAIL>", "supportEmailPlaceholder": "e.g., <EMAIL>", "adminEmailPlaceholder": "e.g., <EMAIL>", "timeZoneRequired": "Default Time Zone is required", "dateFormatRequired": "Date Format is required", "defaultLanguageRequired": "Default Language is required", "invalidEmailFormat": "Please enter a valid email address", "preferencesCreated": "Preferences created successfully", "preferencesUpdated": "Preferences updated successfully", "preferencesDeleted": "Preferences deleted successfully", "nothingToDelete": "Nothing to delete", "confirmDeletePreferences": "Are you sure you want to delete all preferences?", "resetToDefaults": "Reset to Defaults", "confirmResetDefaults": "Are you sure you want to reset all preferences to default values?", "preferencesReset": "Preferences reset to defaults successfully", "savingPreferences": "Saving preferences...", "loadingPreferences": "Loading preferences...", "permissionDenied": "Permission Denied"}, "roles": {"title": "Roles", "date": "Date", "userName": "User name", "ChangedBy": "Changed By", "createNew": "create role", "welcome": "Welcome to Roles Management", "createRole": "Create Role", "editRole": "Edit Role", "deleteRole": "Delete Role", "roleName": "Role Name", "roleDescription": "Role Description", "userRoles": "User Roles", "vendorRoles": "Vend<PERSON>", "actions": "Actions", "confirmDeleteRole": "Are you sure you want to delete this role?", "roleCreated": "Role created successfully", "roleUpdated": "Role updated successfully", "roleDeleted": "Role deleted successfully", "roleNameRequired": "Role name is required", "roleDescriptionRequired": "Role description is required", "roleAlreadyExists": "Role with this name already exists", "noRolesAvailable": "No roles available", "roleNamePlaceholder": "Enter role name", "roleDescriptionPlaceholder": "Enter role description"}, "permissions": {"title": "Permissions", "confirmDeleteTitle": "Delete Permission", "confirmDeleteMessage": "Are you sure you want to delete the permission '{{name}}'?", "permissionNameRequired": "Permission name is required.", "permissionDescriptionRequired": "Description is required.", "permissionsCreatedSuccess": "Permissions created successfully", "permissionUpdatedSuccess": "Permissions Updated successfully", "userPermissions": "User Permissions", "vendorPermissions": "Vendor Permissions", "welcome": "Welcome to Permissions Management", "createPermission": "Create Permission", "permssionName": "Permission Name", "editPermission": "Edit Permission", "deletePermission": "Delete Permission", "permissionName": "Permission Name", "permissionDescription": "Permission Description", "actions": "Actions", "confirmDeletePermission": "Are you sure you want to delete this permission?", "permissionCreated": "Permission created successfully", "permissionUpdated": "Permission updated successfully", "permissionDeleted": "Permission deleted successfully", "permissionDeletedSuccess": "Permission deleted successfully", "permissionAlreadyExists": "Permission with this name already exists", "noPermissionsAvailable": "No permissions available", "permissionNamePlaceholder": "Enter permission name", "permissionDescriptionPlaceholder": "Enter permission description"}, "rolesManagement": {"title": "Roles Management"}, "masterDataManagement": {"title": "Master Data Management"}, "uom": {"title": "Units of Measurement", "welcome": "Welcome to Unit of Measure Management", "createUom": "Create UoM", "editUom": "Edit UoM", "deleteUom": "Delete UoM", "uomCode": "UoM Code", "uomName": "UoM Name", "uomAlias": "UoM Alias", "isActive": "Active", "actions": "Actions", "confirmDeleteUom": "Are you sure you want to delete this Unit of Measure?", "uomCreated": "Unit of Measure created successfully", "uomUpdated": "Unit of Measure updated successfully", "uomDeleted": "Unit of Measure deleted successfully", "uomCodeRequired": "UoM Code is required", "uomNameRequired": "UoM Name is required", "uomAliasRequired": "UoM Alias is required", "uomAlreadyExists": "Unit of Measure with this code already exists", "noUomAvailable": "No Unit of Measures available", "uomCodePlaceholder": "Enter UoM code (e.g., BOX, PLT, PCS)", "uomNamePlaceholder": "Enter UoM name (e.g., <PERSON><PERSON>, Pa<PERSON>t, Pieces)", "uomAliasPlaceholder": "<PERSON><PERSON> alias", "createNew": "Create UoM", "status": "Status", "active": "Active", "inactive": "Inactive"}, "vendors": {"title": "Vendor Management", "welcome": "Welcome to Vendor Management", "createVendor": "Create <PERSON><PERSON><PERSON>", "editVendor": "<PERSON>", "deleteVendor": "Delete Vendor", "vendorName": "Vendor Name", "vendorEmail": "<PERSON><PERSON><PERSON>", "vendorPhone": "Phone Number", "vendorAddress": "Address", "vendorCity": "City", "vendorState": "State", "vendorCountry": "Country", "vendorZipCode": "Zip Code", "vendorWebsite": "Website", "vendorDescription": "Description", "vendorType": "Vendor Type", "vendorStatus": "Status", "contactPerson": "Contact Person", "taxId": "Tax ID", "businessLicense": "Business License", "serviceCategory": "Service Category", "actions": "Actions", "confirmDeleteVendor": "Are you sure you want to delete this vendor?", "vendorCreated": "<PERSON><PERSON><PERSON> created successfully", "vendorUpdated": "Vendor updated successfully", "vendorDeleted": "<PERSON><PERSON><PERSON> deleted successfully", "vendorNameRequired": "Vendor name is required", "vendorEmailRequired": "Vendor email is required", "vendorPhoneRequired": "Phone number is required", "vendorAlreadyExists": "V<PERSON><PERSON> with this email already exists", "noVendorsAvailable": "No vendors available", "vendorNamePlaceholder": "Enter vendor name", "vendorEmailPlaceholder": "Enter vendor email", "vendorPhonePlaceholder": "Enter phone number", "vendorAddressPlaceholder": "Enter address", "vendorDescriptionPlaceholder": "Enter vendor description", "createNew": "Create <PERSON><PERSON><PERSON>"}, "userRoles": {"title": "Assign User Roles", "users": "Users", "uploadCSV": "Select CSV", "upload": "Upload CSV", "selectRoles": "Select Roles", "editUserRoles": "Edit User Roles", "roles": "Roles", "userName": "User name", "businessUsers": "Users", "vendorUsers": "Vend<PERSON>", "welcome": "Welcome to User Roles Management", "createRole": "Create Role", "editRole": "Edit Role", "deleteRole": "Delete Role", "roleName": "Role Name", "roleDescription": "Role Description", "userRoles": "User Roles", "vendorRoles": "Vend<PERSON>", "actions": "Actions", "confirmDeleteTitle": "Remove User Roles", "confirmDeleteMessage": "Are you sure you want to remove all roles from {{userName}}?", "rolesRemovedSuccessfully": "User roles removed successfully", "selectAtLeastOneRole": "Please select at least one role"}, "rolePermissions": {"title": "Assign Role Permissions", "roles": "Roles", "name": "Permission Name", "permissions": "Permissions", "editRolePermissions": "Edit Role Permissions", "roleName": "Role Name", "businessRoles": "Users", "vendorRoles": "Vend<PERSON>", "selectPermissions": "Select Permissions", "welcome": "Welcome to Role Permissions Management", "createRole": "Create Role", "editRole": "Edit Role", "deleteRole": "Delete Role", "roleDescription": "Role Description", "userRoles": "User Roles", "actions": "Actions", "confirmDeleteTitle": "Remove Role Permissions", "confirmDeleteMessage": "Are you sure you want to remove all permissions from role {{roleName}}?", "confirmDeleteRolePermission": "Are you sure you want to delete this role permission?", "permissionsRemovedSuccessfully": "Role permissions removed successfully", "permissionsUpdated": "Role permissions updated successfully", "selectAtLeastOnePermission": "Please select at least one permission"}, "pricing": {"title": "Currency Rates [Base Currency: EURO (EUR)]", "pricingDelete": "Pricing deleted successfully", "pricingdetailsUpdatedSuccessfully": "Pricing details updated successfully", "welcome": "Welcome to Pricing Management", "createPricing": "Add <PERSON>cy", "CuurencyCode": "Currency Code", "currencyValue": "Currency Value", "currencyRatesUpdatedSuccessfully": "Currency rates updated successfully", "editPricing": "Edit Pricing", "deletePricing": "Delete Pricing", "currencyCode": "Currency Code", "cuurencyValue": "Currency Value", "product": "Product", "uom": "UoM", "status": "Status", "price": "Price", "actions": "Actions", "confirmDeletePricing": "Are you sure you want to delete this pricing?", "pricingCreated": "Pricing created successfully", "pricingUpdated": "Pricing updated successfully", "pricingDeleted": "Pricing deleted successfully", "productRequired": "Product is required", "uomRequired": "UoM is required", "priceRequired": "Price is required", "noPricingsAvailable": "No pricings available", "createNew": "Create Pricing"}, "pmicontacts": {"editContact": "PMI Edit Contact", "maptitle": "Map PMI Contacts", "pmiContacts": "PMI Contacts", "title": "PMI Internal Contacts", "deleteContact": "Delete Contact", "selectRoles": "Select Roles", "confirmDeleteContact": "Confirm Delete Contact", "email": "Email", "department": "Departments", "internalContact": "Internal Contacts", "roles": "PMI Roles", "welcome": "Welcome to PMI Contact Management", "createPmiContact": "Create PMI Contact", "contactUpdated": "Contact Updated", "contactCreated": "Contact Created", "contactDeleted": "Contact Deleted", "editPmiContact": "Edit PMI Contact", "deletePmiContact": "Delete PMI Contact", "firstName": "First Name", "lastName": "Last Name", "contactEmail": "Email", "contactPhone": "Phone", "contactType": "Type", "actions": "Actions", "confirmDeletePmiContact": "Are you sure you want to delete this PMI contact?", "pmiContactCreated": "PMI contact created successfully", "pmiContactUpdated": "PMI contact updated successfully", "pmiContactDeleted": "PMI contact deleted successfully", "firstNameRequired": "First Name is required", "lastNameRequired": "Last Name is required"}}