import api from "../httpClient/api";

// Define the Contact type
export interface Contact {
  id?: number;
  first_name: string;
  last_name: string;
  email: string;
 roles: number[];
 updated_by:string;
}


// Get all contacts (with pagination & sorting)
export const getAllContacts = async ({
  queryKey,
}: {
  queryKey: [string, number, number, any];
}): Promise<{
  result: Contact[];
  totalCount: number;
}> => {
  const [_key, page, pageCount, sort] = queryKey;

  const response = await api.get("/user/pmi-contacts", {
    params: {
      page,
      limit: pageCount,
      sortKey: sort?.key || "id",
      sortOrder: sort?.direction || "asc",
    },
  });

  return response.data;
};

// Create a new contact
export const createContact = async (data: Omit<Contact, "id">): Promise<Contact> => {
  const response = await api.post("/user/create-pmi-contacts", data);
  return response.data;
};

// Update an existing contact
export const updateContact = async (
  payload: Contact
): Promise<Contact> => {
  const response = await api.post(`/user/update-pmi-contacts`, payload);
  return response.data;
};


// Delete a contact
export const deleteContact = async (id: number): Promise<void> => {
  await api.delete(`/user/delete-pmi-contacts/${id}`);
};
