// components/VendorPMIContacts.tsx
import { useState, useMemo } from "react";
import {
  Typography,
  Box,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import { useAuthStore } from "../../../store/auth";
import Table, { ColumnType } from "../../../components/Table";
import {
  getMapContactByVendorId,
} from "../../../services/pmi/vendorPMIContacts";
import Loader from "../../../utils/Loader";

function VendorMapContactView() {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  // Removed unused state variables for read-only view

  const [page, setPage] = useState(0);
  const [pageCount, setPageCount] = useState(10);
  const [sort] = useState<any>({});

  const { data: contactsResponse, isLoading } = useQuery({
    queryKey: ["contacts", user?.id, page, pageCount, sort],
    queryFn: ({ queryKey }) => getMapContactByVendorId(Number(queryKey[1])),
    enabled: !!user?.id && user?.role === "vendor",
  });

  const contactsData = useMemo(() => {
    const rawData = contactsResponse?.data || [];
    if (!Array.isArray(rawData)) return [];
    return rawData.map((item: any) => ({
      id: item.id,
      vendor_id: item.vendor?.id,
      department_id: item.department?.id,
      pmi_contact_id: item.pmiContacts?.[0]?.id,
      pmi_contact: item.pmiContacts?.[0] || null,
      departments: item.department ? [item.department] : [],
    }));
  }, [contactsResponse]);

  const totalCount = contactsResponse?.totalCount || 0;

  // Removed unused mutations for read-only view

  const defaultColumns: ColumnType[] = useMemo(
    () => [
      {
      title: t("pmicontacts.department"),
      key: "department_name",
      render: (item: any) =>
        item.departments?.map((d: any) => d.department_name).join(", ") || "-",
    },
    {
      title: t("pmicontacts.internalContact"),
      key: "contact_id",
      render: (item: any) => {
        const c = item.pmi_contact;
        return c ? `${c.first_name} ${c.last_name} (${c.email})` : "-";
      },
    },
      // {
      //   title: t("common.actions"),
      //   key: "actions",
      //   render: (item: VendorPMIContact) => (
      //     <Box sx={{ display: "flex", gap: 1 }}>
      //       {/* <IconButton size="small" onClick={() => handleEdit(item)} color="primary">
      //         <Edit fontSize="small" />
      //       </IconButton> */}
      //       {/* <IconButton size="small" onClick={() => handleDeleteClick(item)} color="error">
      //         <Delete fontSize="small" />
      //       </IconButton> */}
      //     </Box>
      //   ),
      // },
    ],
    [t]
  );

  // Removed unused handlers for read-only view

  // const handleSubmit = () => {
  //   setGeneralError("");
  //   const errors = {
  //     pmi_contact_id: formData.pmi_contact_id
  //       ? ""
  //       : t("pmicontacts.contactRequired"),
  //     department_ids: formData.department_ids.length
  //       ? ""
  //       : t("pmicontacts.departmentRequired"),
  //   };
  //   setFormErrors(errors);
  //   if (errors.pmi_contact_id || errors.department_ids) return;

  //   setIsSubmitting(true);
  //   const payload = { ...formData };

  //   if (editMode && selectedContact) {
  //     updateMutation.mutate({ id: selectedContact.id!, data: payload });
  //   } else {
  //     createMutation.mutate(payload);
  //   }
  //   setIsSubmitting(false);
  // };

  if (isLoading) return <Loader value="Loading..." />;

  if (user?.role !== "vendor") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }
// const openChangeHistory = () => {
//   navigate(`/pmi-mapcontacts-change-history/${formData.vendor_id}`, {
//     state: { backgroundLocation: location },
//   });
// };
  return (
    <>
      <Box m={2}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mb={2}
        >
          <Typography variant="h4">{t("pmicontacts.pmiContacts")}</Typography>
           <Box sx={{ display: "flex", gap: 2 }}>
          {/* <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => {
              resetForm();
              setOpen(true);
            }}
          >
            {t("pmicontacts.maptitle")}
          </Button> */}
            {/* <Button
                        variant="contained"
                        color="primary"
                        onClick={openChangeHistory}
                      >
                        {t("systemPreferences.changeHistory")}
                      </Button> */}
          </Box>
        </Box>

        <Table
          sx={{ height: "auto" }}
          columns={defaultColumns}
          data={contactsData || []}
          pagination={{ totalCount, pageCount, setPageCount, page, setPage }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />
      </Box>
    </>
  );
}

export default VendorMapContactView;
