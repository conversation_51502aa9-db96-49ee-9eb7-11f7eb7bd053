import api from "../httpClient/api";

export interface Permission {
  id?: number;
  permission_name: string;
  description: string;
    type: 'user' | 'vendor';
 createdAt?: string;
  updatedAt?: string;
}

// Get all permissions
export const getAllPermissions = async () => {
  const response = await api.get('/user/permissions');
  return response.data;
};

// Get permission by ID
export const getPermissionById = async (id: number) => {
  const response = await api.get(`/user/permissions/${id}`);
  return response.data;
};

// Create new permission
export const createPermission = async (permissionData: Omit<Permission, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const response = await api.post('/user/create-permission', permissionData);
    return response.data;
  } catch (error) {
    console.error("Error creating permission:", error);
    throw error;
  }
};

// Update permission
export const updatePermission = async (id: number, permissionData: Partial<Permission>) => {
  try {
    const response = await api.post(`/user/update-permission/${id}`, permissionData);
    return response.data;
  } catch (error) {
    console.error("Error updating permission:", error);
    throw error;
  }
};

// Delete permission
export const deletePermission = async (id: number) => {
  try {
    const response = await api.post(`/user/delete-permission/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting permission:", error);
    throw error;
  }
};
