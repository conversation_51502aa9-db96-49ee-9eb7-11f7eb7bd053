import { useState, useEffect, FormEvent, useRef } from "react";
import {
  Box,
  TextField,
  Select,
  MenuItem,
  Button,
  Typography,
  InputLabel,
  FormControl,
  Grid,
  Paper,
  Divider,
  Snackbar,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON>,
  CircularProgress,
} from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  getAllTimeZones,
  getAllGlobalSystemPreferences,
  createGlobalSystemPreferences,
  updateGlobalSystemPreferences,
  uploadLogo,
  uploadFavicon,
} from "../../services/preferences/preferences";
import { snack } from "../../components/toast";
import { toast } from "react-toastify";
import { useAuthStore } from "../../store/auth";
import { useSystemPreferencesStore } from "../../store/systemPreferences";
import { useTranslation } from "react-i18next";
import {
  useReactiveFavicon,
  useReactiveLogo,
} from "../../hooks/useSystemPreferences";
import { useLocation, useNavigate } from "react-router-dom";

const dateFormats = ["YYYY-MM-DD", "MM/DD/YYYY", "DD.MM.YYYY"];

interface GlobalSystemPreferences {
  id?: string;
  default_time_zone: string;
  date_format: string;
  default_language: string;
  default_from_email: string;
  no_reply_email: string;
  support_contact_email: string;
  admin_contact_email: string;
  email_signature: string;
  logoUrl?: string;
  faviconUrl?: string;
}

export default function SystemPreferences() {
  const [data, setData] = useState<Partial<GlobalSystemPreferences>>({});
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [faviconFile, setFaviconFile] = useState<File | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);
  const [faviconPreviewUrl, setFaviconPreviewUrl] = useState<string | null>(
    null
  );
  const [isUploadingLogo, setIsUploadingLogo] = useState<boolean>(false);
  const [isUploadingFavicon, setIsUploadingFavicon] = useState<boolean>(false);
  const logoInputRef = useRef<HTMLInputElement>(null);
  const faviconInputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuthStore();
  console.log("existingPreferences", user?.id);
  const reactiveLogo = useReactiveLogo();
  const reactiveFavicon = useReactiveFavicon();
  const {
    setPreferences,
    setLogoUrl: setStoreLogoUrl,
    setFaviconUrl: setStoreFaviconUrl,
  } = useSystemPreferencesStore();
  const { t } = useTranslation();
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [faviconUrl, setFaviconUrl] = useState<string | null>(null);
  const BASE_URL = import.meta.env.VITE_ASSETS_BASE_URL;

  const { data: timeZones } = useQuery({
    queryKey: ["timezones"],
    queryFn: async () => (await getAllTimeZones()).data,
  });

  const { data: existingPreferences, refetch } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });

  useEffect(() => {
    if (existingPreferences?.result?.length > 0) {
      const preferences = existingPreferences.result[0];
      setData(preferences);
      setPreferences(preferences);
      if (preferences.logoUrl) {
        setLogoUrl(preferences.logoUrl);
        setStoreLogoUrl(preferences.logoUrl);
      }
      if (preferences.faviconUrl) {
        setFaviconUrl(preferences.faviconUrl);

        setStoreFaviconUrl(preferences.faviconUrl);
      }
    }
  }, [
    existingPreferences,
    setPreferences,
    setStoreLogoUrl,
    setStoreFaviconUrl,
  ]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    setData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      const file = e.target.files[0];
      setLogoFile(file);
      const previewUrl = URL.createObjectURL(file);
      setLogoPreviewUrl(previewUrl);
    }
  };

  const handleFaviconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      const file = e.target.files[0];
      setFaviconFile(file);
      const previewUrl = URL.createObjectURL(file);
      setFaviconPreviewUrl(previewUrl);
    }
  };

  const handleError = (err: any): string =>
    err?.response?.data?.message ||
    err?.message ||
    "An unknown error occurred.";

  const { mutate: createPreferences } = useMutation({
    mutationFn: createGlobalSystemPreferences,
    onSuccess: () => {
      snack.success(t("systemPreferences.preferencesCreated"));
      refetch();
    },
    onError: (err) => snack.error(handleError(err)),
  });

  const { mutate: updatePreferences } = useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: string;
      payload: Record<string, any>;
    }) => updateGlobalSystemPreferences(id, payload), // Now accepts a JSON object
    onSuccess: () => {
      toast.success(t("systemPreferences.preferencesUpdated"));
      setSnackbarOpen(true);
      refetch();
    },
    onError: (err) => snack.error(handleError(err)),
  });

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!data.default_time_zone)
      return snack.error(t("systemPreferences.timeZoneRequired"));
    if (!data.date_format)
      return snack.error(t("systemPreferences.dateFormatRequired"));
    if (!data.default_language)
      return snack.error(t("systemPreferences.defaultLanguageRequired"));

    // Build JSON payload
    const payload: Record<string, any> = {};
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        payload[key] = value;
      }
    });

    // Decide between update and create
    if (data.id) {
      const { id, ...rest } = payload;
      rest.updated_by = String(user?.id); // Add updated_by from the current user
      updatePreferences({ id: data.id, payload: rest });
    } else {
      createPreferences({ payload }); // Adjust createPreferences accordingly
    }
  };

  const handleUploadLogo = async () => {
    if (!logoFile) return snack.error(t("systemPreferences.noLogoSelected"));

    setIsUploadingLogo(true);

    try {
      const formData = new FormData();
      // Create a new file with the name "logo.png" to ensure backend saves it correctly
      const fileExtension = logoFile.name.split(".").pop() || "png";
      const renamedFile = new File([logoFile], `logo.${fileExtension}`, {
        type: logoFile.type,
      });
      formData.append("file", renamedFile);

      const response = await uploadLogo(formData);

      // Show success message
      snack.success(t("systemPreferences.logoUploaded"));

      if (response?.imageUrl) {
        // Add cache-busting parameter to force React to re-render
        const timestamp = Date.now();
        const logoUrl = `${response.imageUrl}?${timestamp}`;

        // Update local state
        setLogoUrl(logoUrl);
        setLogoPreviewUrl(null); // Clear preview, use uploaded URL

        // Update the data state to include the logo URL
        setData((prev) => ({
          ...prev,
          logoUrl: logoUrl,
        }));

        // Update store immediately for reactive updates
        setStoreLogoUrl(logoUrl);

        // Auto-save the preferences with the new logo URL
        if (data.id) {
          const updatedData = { ...data, logoUrl: logoUrl };
          const updateFormData = new FormData();
          Object.entries(updatedData).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              updateFormData.append(key, value as string);
            }
          });
          await updateGlobalSystemPreferences(data.id, updateFormData);
        }
      }

      // Refetch data
      refetch();

      // Clear the file input and reset state
      setLogoFile(null);
      setLogoPreviewUrl(null);
      if (logoInputRef.current) {
        logoInputRef.current.value = "";
      }

      // Hard refresh after successful upload
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err: any) {
      snack.error(handleError(err));
    } finally {
      setIsUploadingLogo(false);
    }
  };

  const handleUploadFavicon = async () => {
    if (!faviconFile)
      return snack.error(t("systemPreferences.noFaviconSelected"));
    setIsUploadingFavicon(true);

    try {
      const formData = new FormData();
      formData.append("file", faviconFile);
      const response = await uploadFavicon(formData);

      // Show success message
      snack.success(t("systemPreferences.faviconUploaded"));

      if (response?.imageUrl) {
        // Add cache-busting parameter to force React to re-render
        const timestamp = Date.now();
        const faviconUrl = `${response.imageUrl}?${timestamp}`;

        // Update local state
        setFaviconUrl(faviconUrl);
        setFaviconPreviewUrl(null); // Clear preview, use uploaded URL

        // Update the data state to include the favicon URL
        setData((prev) => ({
          ...prev,
          faviconUrl: faviconUrl,
        }));

        // Update store immediately for reactive updates
        setStoreFaviconUrl(faviconUrl);

        // Auto-save the preferences with the new favicon URL
        if (data.id) {
          const updatedData = { ...data, faviconUrl: faviconUrl };
          const updateFormData = new FormData();
          Object.entries(updatedData).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              updateFormData.append(key, value as string);
            }
          });
          await updateGlobalSystemPreferences(data.id, updateFormData);
        }
      }

      // Refetch data
      refetch();

      // Clear the file input and reset state
      setFaviconFile(null);
      setFaviconPreviewUrl(null);
      if (faviconInputRef.current) {
        faviconInputRef.current.value = "";
      }

      // Hard refresh after successful upload
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err: any) {
      snack.error(handleError(err));
    } finally {
      setIsUploadingFavicon(false);
    }
  };
  const navigate = useNavigate();
  const location = useLocation();

  const openChangeHistory = () => {
    navigate("/system-preferences-change-history", {
      state: { backgroundLocation: location },
    });
  };
  if (user?.role === "business") {
    return (
      <>
        <Box display="flex" justifyContent="end">
          <Button
            variant="contained"
            color="primary"
            onClick={openChangeHistory}
          >
            {t("systemPreferences.changeHistory")}
          </Button>
        </Box>
        <Box p={4}>
          <Typography variant="h4" gutterBottom>
            {t("systemPreferences.title")}
          </Typography>

          {/* Debug Component - Remove in production */}
          {/* <SystemPreferencesDebug /> */}
          {/* Branding */}
          <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              {t("systemPreferences.brandingMaterial")}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              {/* Logo Upload */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                  {t("systemPreferences.uploadLogo")}
                </Typography>
                <Button component="label" fullWidth variant="outlined">
                  {t("systemPreferences.uploadLogo")}
                  <input
                    ref={logoInputRef}
                    type="file"
                    hidden
                    accept="image/png,image/jpeg,image/jpg,image/svg+xml"
                    onChange={handleLogoChange}
                  />
                </Button>
                {reactiveLogo && (
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2">
                      Current Logo Preview:
                    </Typography>
                    <img
                      src={reactiveLogo}
                      alt="Current Logo"
                      style={{
                        maxHeight: "50px",
                        maxWidth: "100px",
                        border: "1px solid #ccc",
                      }}
                    />
                  </Box>
                )}
                {logoFile && (
                  <Box mt={1}>
                    <Typography variant="body2">{logoFile.name}</Typography>
                    <Button
                      variant="contained"
                      onClick={() => {
                        handleUploadLogo();
                      }}
                      disabled={isUploadingLogo}
                      sx={{ mt: 1 }}
                      startIcon={
                        isUploadingLogo ? <CircularProgress size={20} /> : null
                      }
                    >
                      {isUploadingLogo
                        ? t("systemPreferences.uploadingLogo")
                        : t("systemPreferences.uploadNow")}
                    </Button>
                  </Box>
                )}

                {/* Logo Preview section */}
                {(logoPreviewUrl || logoUrl) && (
                  <Box mt={2}>
                    <Typography variant="subtitle2">
                      {t("systemPreferences.logoPreview")}
                    </Typography>
                    <img
                      src={
                        logoPreviewUrl ||
                        (logoUrl
                          ? logoUrl.startsWith("http")
                            ? logoUrl
                            : `${BASE_URL}${logoUrl}`
                          : "")
                      }
                      alt="Logo Preview"
                      style={{
                        maxWidth: "200px",
                        maxHeight: "200px",
                        marginTop: "8px",
                      }}
                    />
                  </Box>
                )}
              </Grid>

              {/* Favicon Upload */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                  {t("systemPreferences.uploadFavicon")}
                </Typography>
                <Button component="label" fullWidth variant="outlined">
                  {t("systemPreferences.uploadFavicon")}
                  <input
                    ref={faviconInputRef}
                    type="file"
                    hidden
                    accept="image/svg+xml"
                    onChange={handleFaviconChange}
                  />
                </Button>

                <Box mt={1}>
                  {/* SVG format warning shown initially */}
                  <Typography variant="caption" color="error" sx={{ mb: 1 }}>
                    * Upload in SVG format only
                  </Typography>

                  {faviconFile && (
                    <>
                      <Typography variant="body2">
                        {faviconFile.name}
                      </Typography>

                      <Button
                        variant="contained"
                        onClick={() => {
                          handleUploadFavicon();
                        }}
                        disabled={isUploadingFavicon}
                        sx={{ mt: 1 }}
                        startIcon={
                          isUploadingFavicon ? (
                            <CircularProgress size={20} />
                          ) : null
                        }
                      >
                        {isUploadingFavicon
                          ? t("systemPreferences.uploadingFavicon")
                          : t("systemPreferences.uploadNow")}
                      </Button>
                    </>
                  )}
                </Box>

                {/* Favicon Preview section */}
                {(faviconPreviewUrl || faviconUrl) && (
                  <Box mt={2}>
                    <Typography variant="subtitle2">
                      {t("systemPreferences.faviconPreview")}
                    </Typography>
                    <img
                      src={
                        faviconPreviewUrl ||
                        (faviconUrl
                          ? faviconUrl.startsWith("http")
                            ? faviconUrl
                            : `${
                                import.meta.env.VITE_ASSETS_FAVICON_URL
                              }${faviconUrl}`
                          : "")
                      }
                      alt="Favicon Preview"
                      style={{
                        maxWidth: "32px",
                        maxHeight: "32px",
                        marginTop: "8px",
                      }}
                    />
                  </Box>
                )}
                {reactiveFavicon && (
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2">
                      Cuurent Favicon Preview:
                    </Typography>
                    <img
                      src={reactiveFavicon}
                      alt="Current Favicon"
                      style={{
                        maxHeight: "32px",
                        maxWidth: "32px",
                        border: "1px solid #ccc",
                      }}
                    />
                  </Box>
                )}
              </Grid>
            </Grid>
          </Paper>

          <form onSubmit={handleSubmit}>
            {/* Global Settings */}
            <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
              <Typography variant="h4" gutterBottom>
                {t("systemPreferences.globalSettings")}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>
                      {t("systemPreferences.defaultTimeZone")}
                    </InputLabel>
                    <Select
                      name="default_time_zone"
                      value={data.default_time_zone ?? ""}
                      onChange={handleChange}
                      label="Default Time Zone"
                    >
                      {timeZones?.data?.map((tz: any, i: number) => (
                        <MenuItem key={i} value={tz.name}>
                          {tz.name} ({tz.abbrev}, UTC
                          {tz.utc_offset.hours >= 0
                            ? `+${tz.utc_offset.hours}.00`
                            : `${tz.utc_offset.hours}.00`}
                          )
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>
                      {t("systemPreferences.dateFormatLabel")}
                    </InputLabel>
                    <Select
                      name="date_format"
                      value={data.date_format ?? ""}
                      onChange={handleChange}
                      label="Date Format"
                    >
                      {dateFormats.map((format) => (
                        <MenuItem key={format} value={format}>
                          {format}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>
                      {t("systemPreferences.defaultLanguage")}
                    </InputLabel>
                    <Select
                      name="default_language"
                      value={data.default_language ?? ""}
                      onChange={handleChange}
                      label="Default Language"
                    >
                      {languages.map((lang) => (
                        <MenuItem key={lang} value={lang}>
                          {lang}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid> */}
              </Grid>
            </Paper>

            {/* Email Settings */}
            <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                {t("systemPreferences.emailSettings")}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Grid container spacing={2}>
                {[
                  {
                    label: t("systemPreferences.defaultFromEmail"),
                    name: "default_from_email",
                    placeholder: t(
                      "systemPreferences.defaultFromEmailPlaceholder"
                    ),
                  },
                  {
                    label: t("systemPreferences.noReplyEmail"),
                    name: "no_reply_email",
                    placeholder: t("systemPreferences.noReplyEmailPlaceholder"),
                  },
                  {
                    label: t("systemPreferences.supportEmail"),
                    name: "support_contact_email",
                    placeholder: t("systemPreferences.supportEmailPlaceholder"),
                  },
                  {
                    label: t("systemPreferences.adminEmail"),
                    name: "admin_contact_email",
                    placeholder: t("systemPreferences.adminEmailPlaceholder"),
                  },
                ].map((field, i) => (
                  <Grid item xs={12} md={6} key={i}>
                    <TextField
                      label={field.label}
                      name={field.name}
                      value={
                        data[field.name as keyof GlobalSystemPreferences] ?? ""
                      }
                      onChange={handleChange}
                      fullWidth
                      placeholder={field.placeholder}
                    />
                  </Grid>
                ))}
              </Grid>
            </Paper>

            {/* Email Signature */}
            <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                {t("systemPreferences.emailSignature")}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <TextField
                name="email_signature"
                value={data.email_signature ?? ""}
                onChange={handleChange}
                multiline
                rows={6}
                fullWidth
                placeholder={t("systemPreferences.emailSignaturePlaceholder")}
              />
            </Paper>

            {/* Action Buttons */}
            <Box display="flex" justifyContent="end">
              <Button variant="contained" color="primary" type="submit">
                {t("systemPreferences.savePreferences")}
              </Button>
            </Box>
          </form>
        </Box>

        <Snackbar
          open={snackbarOpen}
          autoHideDuration={4000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <MuiAlert
            onClose={() => setSnackbarOpen(false)}
            severity="info"
            sx={{ width: "100%" }}
          >
            {t("auth.preferencesUpdated")}
          </MuiAlert>
        </Snackbar>
      </>
    );
  }

  if (user?.role === "vendor") {
    return (
      <Box textAlign="center" mt={10}>
        <Typography variant="h4" gutterBottom>
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return null;
}
