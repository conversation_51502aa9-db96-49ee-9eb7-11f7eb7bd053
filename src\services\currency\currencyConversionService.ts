import api from "../httpClient/api";

export interface Currency {
  id?: number;
  currency_code: string;
  currency_value: number | null;
  isActive: boolean;
}

// Get all UoMs
export const getAllCurrencyCodes = async () => {
  const response = await api.get("/user/currency-codes");
  return response.data;
};

// Get UoM by ID
export const getUoMById = async (id: number) => {
  const response = await api.get(`/user/uoms/${id}`);
  return response.data;
};

// Create new createCurrency
// services/currencyService.ts
// Support creating multiple currencies
export const createCurrency = async (
  currenciesData: Omit<Currency, "id">[]
) => {
  try {
    const payload = {
      currencies: currenciesData, // Use the whole array directly
    };
    const response = await api.post("/user/create-currency-code", payload);
    return response.data;
  } catch (error) {
    console.error("Error creating currencies:", error);
    throw error;
  }
};

// Update UoM
export const updateCurrency = async (
  id: number,
  uomData: Partial<Currency>
) => {
  try {
    const response = await api.post(`/user/currency-code/${id}`, uomData);
    return response.data;
  } catch (error) {
    console.error("Error updating UoM:", error);
    throw error;
  }
};

// Delete UoM
export const deleteCurrencyCode = async (id: number) => {
  try {
    const response = await api.post(`/user/delete-currency-code/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting UoM:", error);
    throw error;
  }
};

export const updateCurrencyCode = async (
  updates: {
    id: number;
    currency_code: string;
    currency_value: string;
    isActive: boolean;
  }[]
) => {
  try {
    const response = await api.post(`/user/update-currency-code`, {
      updates,
    });
    return response.data;
  } catch (error) {
    console.error("Error updateCurrencyCode:", error);
    throw error;
  }
};

// Toggle UoM active status
export const toggleUoMStatus = async (id: number, isActive: boolean) => {
  try {
    const response = await api.post(`/user/toggle-uom-status/${id}`, {
      isActive,
    });
    return response.data;
  } catch (error) {
    console.error("Error toggling UoM status:", error);
    throw error;
  }
};
