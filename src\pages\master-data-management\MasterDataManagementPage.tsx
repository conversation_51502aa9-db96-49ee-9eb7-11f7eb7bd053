import { useEffect } from "react";
import { Box } from "@mui/material";
import { useNavigate, useLocation, Routes, Route } from "react-router-dom";
import { useAuthStore } from "../../store/auth";
import UsersPage from "../users/UsersPage";
import UomPage from "../uom/UomPage";
import CurrencyConversionPage from "../currency/CurrencyConversionPage";
import PMInternalContactsPage from "./pmi/pmiInternal-contacts";

function MasterDataManagementPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuthStore();

  useEffect(() => {
    // If we're on the parent route without a child, redirect based on user role
    if (location.pathname === "/master-data-management" || location.pathname === "/master-data-management/") {
      if (user?.role === "vendor") {
        navigate("/master-data-management/users", { replace: true });
      } else {
        navigate("/master-data-management/users", { replace: true });
      }
    }
  }, [location.pathname, navigate, user?.role]);

  return (
    <Box>

  {/* Child Route Content - No tabs here, just render the pages directly */}
  <Routes>
    <Route path="users" element={<UsersPage />} />
     <Route path="internal-contacts" element={<PMInternalContactsPage />} />
    <Route path="uom" element={<UomPage />} />
    <Route path="currency" element={<CurrencyConversionPage />} />
  </Routes>
</Box>

  );
}

export default MasterDataManagementPage;
