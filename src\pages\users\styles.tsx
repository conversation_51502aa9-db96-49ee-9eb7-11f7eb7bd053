import { styled, Typography } from "@mui/material";

// Container for the nav bar
export const StyledProfileNav = styled("div")(() => ({
  background: "#5583BC",
  display: "flex",
  justifyContent: "flex-start", // Align items to the left
  gap: 20,
  padding: "10px 20px",

  '@media (max-width: 768px)': {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    justifyContent: 'flex-start',
    gap: 10,
    overflowX: 'auto',
    whiteSpace: 'nowrap',
    padding: "10px",
  },
}));

// Individual nav item
export const StyledProfileNavItem = styled(Typography)<{ active: number }>(
  ({ theme, active }) => ({
    padding: "10px 20px",
    borderRadius: "8px",
    color: active ? "#fff" : "#DDE9F9",
    backgroundColor: active ? theme.palette.primary.dark : "#6FA8DC",
    fontWeight: active ? 600 : 500,
    fontSize: "16px",
    position: "relative",
    cursor: "pointer",
    transition: "all 0.3s ease",

    "&:hover": {
      backgroundColor: active ? theme.palette.primary.dark : "#8AB9E7",
      color: "#fff",
    },

    ...(active && {
      "&:before": {
        position: "absolute",
        content: "''",
        bottom: -2,
        left: "50%",
        transform: "translateX(-50%)",
        width: "50%",
        height: 3,
        backgroundColor: "#fff",
        borderRadius: "2px",
      },
    }),
  })
);
