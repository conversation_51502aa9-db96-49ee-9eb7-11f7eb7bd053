import { CircularProgress } from "@mui/material";
import { Box } from "@mui/system";

interface LoaderProps {
  minHeight?: string | number;
  color?: string;
  value?: string;
}

function Loader(props: LoaderProps) {
  const { minHeight = 300, color = "primary.main" } = props;
  return (
    <Box
      sx={{
        minHeight,
        width: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      
      <CircularProgress sx={{ color }}  />
      {props?.value && <span style={{marginLeft: '20px'}}>{props?.value}</span>}
       
    </Box>
  );
}

export default Loader;
