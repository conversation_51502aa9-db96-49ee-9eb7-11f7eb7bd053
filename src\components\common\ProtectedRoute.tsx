import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { Box, CircularProgress } from "@mui/material";
import { useAuthStore } from "../../store/auth";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading, token, hasCompleted2FA } = useAuthStore();
  const location = useLocation();

  // Show loading spinner if auth is still initializing
  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Not logged in: redirect to login
  if (!token || !isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Logged in but 2FA incomplete: redirect to /two-factor
  if (!hasCompleted2FA && location.pathname !== "/two-factor") {
    return <Navigate to="/two-factor" state={{ from: location }} replace />;
  }

  // Already completed 2FA and authenticated
  return <>{children}</>;
};

export default ProtectedRoute;
