import {
  Box,
  Button,
  Grid,
  <PERSON>put<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { snack } from "../../components/toast";
import {
  getAllmailTemplates,
  updateNotificationTemplate,
} from "../../services/notification/notification";
import { useNavigate, useParams } from "react-router-dom";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";

function NotificationTemplateEdit() {
  const [title, setTitle] = useState("");
  const [subject, setSubject] = useState("");
  const [message_body, setContent] = useState("");
  const [notification_code, setCode] = useState("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const { t } = useTranslation();

  const navigate = useNavigate();
  const { id } = useParams();

  const { data: existingPreferences } = useQuery({
    queryKey: ["getAllmailTemplates"],
    queryFn: async () => {
      const response = await getAllmailTemplates();
      return response.result;
    },
  });

  const matchedTemplate = Array.isArray(existingPreferences)
    ? existingPreferences.find(
        (vendor: any) => String(vendor.id) === String(id)
      )
    : null;

  // ✅ Populate form state when matchedTemplate is available
  useEffect(() => {
    if (matchedTemplate) {
      setTitle(matchedTemplate.notification_title || "");
      setSubject(matchedTemplate.subject || "");
      setContent(matchedTemplate.message_body || "");
      setCode(matchedTemplate.notification_code || "");
    }
  }, [matchedTemplate]);

  const { mutate: updateTemplate, isPending } = useMutation({
    mutationFn: (data: {
      notification_code: string;
      notification_title: string;
      subject: string;
      message_body: string;
    }) => updateNotificationTemplate(id as string, data),
    onSuccess: () => {
      snack.success(t("notification.templateUpdated"));
      setSnackbarOpen(true);
      setTimeout(() => {
        navigate("/notification-preferences");
      }, 1500);
    },
    onError: (error: any) => {
      console.error("Error updating template:", error);
      snack.error(t("notification.templateRequired"));
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const payload = {
      notification_code,
      notification_title: title,
      subject,
      message_body,
    };
    updateTemplate(payload);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Grid container spacing={2} mt={2} flexDirection="column" px={3}>
        <Typography variant="h6" color="primary">
          {t("notification.editTemplate")}
        </Typography>

        <Box mt={2}>
          <InputLabel>{t("notification.titleLabel")}</InputLabel>
          <TextField
            name="title"
            size="small"
            fullWidth
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            required
            sx={{ width: "50%" }}
            inputProps={{ minLength: 3, maxLength: 50 }}
          />
        </Box>

        <Box mt={2}>
          <InputLabel>{t("notification.subject")} *</InputLabel>
          <TextField
            name="subject"
            size="small"
            fullWidth
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            required
            sx={{ width: "50%" }}
            inputProps={{ minLength: 3, maxLength: 50 }}
            placeholder={t("notification.subject")}
          />
        </Box>

        <Box mt={3} width="100%">
          <InputLabel>{t("notification.emailBody")} *</InputLabel>
          <ReactQuill
            theme="snow"
            value={message_body}
            onChange={setContent}
            style={{ height: "200px", marginBottom: "50px" }}
          />
        </Box>
      </Grid>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
          px: 3,
          mt: 2,
        }}
      >
        <Button variant="outlined" color="primary" onClick={() => navigate(-1)}>
          {t("common.back")}
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="secondary"
          disabled={isPending}
        >
          {isPending ? t("notification.updatingTemplate") : t("notification.saveTemplate")}
        </Button>
      </Box>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <MuiAlert
          onClose={() => setSnackbarOpen(false)}
          severity="success"
          sx={{ width: "100%" }}
        >
          {t("auth.mailTemplateUpdated")}
        </MuiAlert>
      </Snackbar>
    </form>
  );
}

export default NotificationTemplateEdit;
