import { createTheme, responsiveFontSizes } from "@mui/material/styles";
import { ThemeMode } from "../types";

declare module "@mui/material/styles" {
  interface Palette {
    neutral: Palette["primary"];
  }
  interface PaletteOptions {
    neutral: PaletteOptions["primary"];
  }
}

export const getTheme = (mode: ThemeMode) => {
  const baseTheme = createTheme({
    palette: {
      mode,
      primary: {
        main: "#004B8D",
        //  main: '#00305B', /
        light: "#757de8",
        dark: "#002984",
        contrastText: "#fff",
      },
      secondary: {
        main: "#536dfe", // Indigo accent
        light: "#8c9eff",
        dark: "#0043ca",
        contrastText: "#fff",
      },
      error: {
        main: "#f44336", // Red
        light: "#e57373",
        dark: "#d32f2f",
      },
      warning: {
        main: "#ff9800", // Orange
        light: "#ffb74d",
        dark: "#f57c00",
      },
      info: {
        main: "#2196f3", // Blue
        light: "#64b5f6",
        dark: "#1976d2",
      },
      success: {
        main: "#4caf50", // Green
        light: "#81c784",
        dark: "#388e3c",
      },
      neutral: {
        main: "#64748B",
        light: "#94A3B8",
        dark: "#334155",
        contrastText: "#fff",
      },
      background: {
        default: mode === "light" ? "#f5f5f5" : "#121212",
        paper: mode === "light" ? "#ffffff" : "#1e1e1e",
      },
      text: {
        primary:
          mode === "light"
            ? "rgba(0, 0, 0, 0.87)"
            : "rgba(255, 255, 255, 0.87)",
        secondary:
          mode === "light" ? "rgba(0, 0, 0, 0.6)" : "rgba(255, 255, 255, 0.6)",
      },
    },
    typography: {
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      h1: {
        fontWeight: 700,
        fontSize: "2.5rem",
        lineHeight: 1.2,
      },
      h2: {
        fontWeight: 700,
        fontSize: "2rem",
        lineHeight: 1.2,
      },
      h3: {
        fontWeight: 600,
        fontSize: "1.75rem",
        lineHeight: 1.2,
      },
      h4: {
        fontWeight: 600,
        fontSize: "1.5rem",
        lineHeight: 1.2,
      },
      h5: {
        fontWeight: 600,
        fontSize: "1.25rem",
        lineHeight: 1.2,
      },
      h6: {
        fontWeight: 600,
        fontSize: "1rem",
        lineHeight: 1.2,
      },
      subtitle1: {
        fontWeight: 500,
        fontSize: "1rem",
      },
      subtitle2: {
        fontWeight: 500,
        fontSize: "0.875rem",
      },
      body1: {
        fontWeight: 400,
        fontSize: "1rem",
        lineHeight: 1.5,
      },
      body2: {
        fontWeight: 400,
        fontSize: "0.875rem",
        lineHeight: 1.5,
      },
      button: {
        fontWeight: 500,
        fontSize: "0.875rem",
        textTransform: "none",
      },
    },
    shape: {
      borderRadius: 8,
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: "none",
            borderRadius: 8,
            padding: "8px 16px",
            boxShadow: "none",
            "&:hover": {
              boxShadow: "none",
            },
          },
          contained: {
            boxShadow: "none",
            "&:hover": {
              boxShadow: "none",
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            boxShadow:
              mode === "light"
                ? "0px 2px 4px rgba(0, 0, 0, 0.05), 0px 4px 6px rgba(0, 0, 0, 0.05)"
                : "0px 2px 4px rgba(0, 0, 0, 0.2), 0px 4px 6px rgba(0, 0, 0, 0.2)",
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundImage: "none",
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            boxShadow:
              mode === "light"
                ? "0px 1px 3px rgba(0, 0, 0, 0.05)"
                : "0px 1px 3px rgba(0, 0, 0, 0.2)",
          },
        },
      },
      MuiDrawer: {
        styleOverrides: {
          paper: {
            border: "none",
          },
        },
      },
      MuiInputBase: {
        styleOverrides: {
          root: {
            borderRadius: 8,
          },
        },
      },
      MuiOutlinedInput: {
        styleOverrides: {
          root: {
            borderRadius: 8,
          },
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            borderRadius: 8,
          },
        },
      },
    },
  });

  return responsiveFontSizes(baseTheme);
};

export default getTheme;
