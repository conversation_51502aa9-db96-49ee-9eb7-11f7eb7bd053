import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  MenuItem,
  TextField,
  Typography,
  Paper,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { useAuthStore } from "../../store/auth";
import VendorStepper from "../../components/steppedbar/VendorStepper";
import { getAllRoles } from "../../services/roles/roles";
import { getAllvendors } from "../../services/vendor/vendor";
import api from "../../services/httpClient/api";
import { stepRoutes } from "../../utils";
import { RadioGroup, FormControlLabel, Radio } from "@mui/material";
import { useSnackbar } from "../../utils/SnackbarProvider";

interface ContactDetail {
  id:number,
  vendor: number | { id: number }; // handle both formats
  contact_type: string;
  title: string;
  first_name: string;
  last_name: string;
  email: string;
  office_number: string;
  mobile_number: string;
  gender: string;
}

const VendorContactDetailsPage: React.FC = () => {
  const { user } = useAuthStore();
  const { showMessage } = useSnackbar();
  const emptyContact: ContactDetail = {
    vendor: Number(user?.id) || 0,
    contact_type: "",
    title: "",
    first_name: "",
    last_name: "",
    email: "",
    office_number: "",
    mobile_number: "",
    gender: "",
  };
  const [contactList, setContactList] = useState<ContactDetail[]>([
    emptyContact,
  ]);
  const [hasExistingContacts, setHasExistingContacts] = useState(false);
  const navigate = useNavigate();
    const params = useParams();
  const { id } = params;

  // Step index for the VendorStepper (1 = Contact Details)
  // const currentStep = 1;
  const [currentStep, setCurrentStep] = useState(1); // 0 = Company Profile

  // Fetch vendor roles
  const { data: rolesData = [] } = useQuery({
    queryKey: ["roles", user?.role],
    queryFn: async () => {
      if (!user) return [];
      const response = await getAllRoles();
      return (
        response.result?.filter((role: any) => role.userType === user.role) ||
        []
      );
    },
    enabled: !!user,
  });
  const { data: existingPreferences } = useQuery({
    queryKey: ["getAllvendors"],
    queryFn: async () => {
      const response = await getAllvendors();
      return response.result;
    },
  });

  const matchedVendor =
    existingPreferences && Array.isArray(existingPreferences)
      ? existingPreferences.find(
          (vendor: { name: string; email: string }) =>
            vendor.email?.toLowerCase().trim() ===
            user?.email?.toLowerCase().trim()
        ) || null
      : null;

  // Fetch existing contact details
  useEffect(() => {
    api.get("/user/contact-details").then((res) => {
      if (Array.isArray(res.data) && res.data.length > 0) {
        setContactList(res.data);
        setHasExistingContacts(true);
      } else {
        setContactList([emptyContact]);
        setHasExistingContacts(false);
      }
    }).catch((error) => {
      console.error("Error fetching contact details:", error);
      setContactList([emptyContact]);
      setHasExistingContacts(false);
    });
  }, []);

  const handleInputChange = (index: number, field: string, value: string) => {
    const updatedContacts = [...contactList];
    updatedContacts[index] = {
      ...updatedContacts[index],
      [field]: value,
    };
    setContactList(updatedContacts);
  };

  const handleAddNewContact = () => {
    setContactList([...contactList, { ...emptyContact }]);
  };

  const handleDeleteContact = (index: number) => {
    if (contactList.length === 1) return;
    const updatedList = contactList.filter((_, i) => i !== index);
    setContactList(updatedList);
  };
  const handleSubmit = async () => {
    try {
      if (hasExistingContacts) {
        // Update existing contacts
        const updatePayload = {
          updates: contactList.map(contact => ({
            id: contact.id,
            vendor: typeof contact.vendor === "object" ? contact.vendor.id : Number(user?.id),
            contact_type: contact.contact_type,
            title: contact.title,
            first_name: contact.first_name,
            last_name: contact.last_name,
            email: contact.email,
            office_number: contact.office_number,
            mobile_number: contact.mobile_number,
            gender: contact.gender,
          })),
        };

        const response = await api.post("/user/update-contact-details", updatePayload);

        if (response.data?.success) {
          showMessage("Contacts updated successfully!", "success");
          navigate("/vendor-onboarding/location-details");
        } else {
          showMessage("Something went wrong while updating contacts.", "error");
        }
      } else {
        // Create new contacts
        const createPayload = {
          contacts: contactList.map(contact => ({
            id: contact.id,
            vendor: typeof contact.vendor === "object" ? contact.vendor.id : Number(user?.id),
            contact_type: contact.contact_type,
            title: contact.title,
            first_name: contact.first_name,
            last_name: contact.last_name,
            email: contact.email,
            office_number: contact.office_number,
            mobile_number: contact.mobile_number,
            gender: contact.gender,
          })),
        };

        const response = await api.post("/user/create-contact-details", createPayload);

        if (response.data?.success) {
          showMessage("Contacts saved successfully!", "success");
          navigate("/vendor-onboarding/location-details");
        } else {
          showMessage("Something went wrong while saving contacts.", "error");
        }
      }
    } catch (error: any) {
      console.error("Error saving contacts:", error);
      showMessage(
        error?.response?.data?.message ||
          "An error occurred while saving contacts.",
        "error"
      );
    }
  };

  return (
    <>
      <VendorStepper activeStep={currentStep} />

      <Box px={3} py={2}>
        <Typography variant="h6" gutterBottom>
          Contact Details
        </Typography>

        {contactList.map((contact, index) => (
          <Paper
            key={index}
            sx={{
              padding: 3,
              borderRadius: 2,
              mb: 3,
              backgroundColor: "#f9f9f9",
            }}
          >
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Contact Type *"
                  value={contact.contact_type}
                  onChange={(e) =>
                    handleInputChange(index, "contact_type", e.target.value)
                  }
                  fullWidth
                  select
                >
                  {rolesData.map((role: any) => (
                    <MenuItem key={role.id} value={role.role_name}>
                      {role.role_name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="Title *"
                  value={contact.title}
                  onChange={(e) =>
                    handleInputChange(index, "title", e.target.value)
                  }
                  fullWidth
                  select
                >
                  <MenuItem value="Mr">Mr</MenuItem>
                  <MenuItem value="Ms">Ms</MenuItem>
                  <MenuItem value="Mrs">Mrs</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="First Name *"
                  value={
                    contactList[index]?.first_name
                  }
                  onChange={(e) =>
                    handleInputChange(index, "first_name", e.target.value)
                  }
                  fullWidth
                  inputProps={{ maxLength: 50 }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="Last Name *"
                  value={
                    contactList[index]?.last_name
                  }
                  onChange={(e) =>
                    handleInputChange(index, "last_name", e.target.value)
                  }
                  fullWidth
                  inputProps={{ maxLength: 50 }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Gender *
                </Typography>
                <RadioGroup
                  row
                  value={contact.gender}
                  onChange={(e) =>
                    handleInputChange(index, "gender", e.target.value)
                  }
                >
                  <FormControlLabel
                    value="male"
                    control={<Radio />}
                    label="Male"
                  />
                  <FormControlLabel
                    value="female"
                    control={<Radio />}
                    label="Female"
                  />
                </RadioGroup>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="Email *"
                  value={contact.email}
                  onChange={(e) =>
                    handleInputChange(index, "email", e.target.value)
                  }
                  fullWidth
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="Office Phone *"
                  value={contact.office_number}
                  onChange={(e) =>
                    handleInputChange(
                      index,
                      "office_number",
                      e.target.value
                    )
                  }
                  fullWidth
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="Mobile Phone *"
                  value={contact.mobile_number}
                  onChange={(e) =>
                    handleInputChange(
                      index,
                      "mobile_number",
                      e.target.value
                    )
                  }
                  fullWidth
                />
              </Grid>
            </Grid>

            <Box display="flex" justifyContent="flex-end" mt={2}>
              <Button
                variant="outlined"
                color="error"
                onClick={() => handleDeleteContact(index)}
                disabled={contactList.length === 1}
              >
                Delete
              </Button>
            </Box>
          </Paper>
        ))}

        <Box display="flex" justifyContent="flex-end" mb={2}>
          <Button variant="contained" onClick={handleAddNewContact}>
            New Contact
          </Button>
        </Box>

        <Box display="flex" justifyContent="space-between">
          <Button
            variant="contained"
            onClick={() => {
              if (currentStep > 0) {
                navigate(stepRoutes[currentStep - 1]);
              }
            }}
          >
            Back
          </Button>

          <Button variant="contained" color="primary" onClick={handleSubmit}>
            {hasExistingContacts ? "Update & Next" : "Save & Next"}
          </Button>
        </Box>
      </Box>
    </>
  );
};

export default VendorContactDetailsPage;
