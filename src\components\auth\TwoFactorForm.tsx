import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  TextField,
  Button,
  Box,
  CircularProgress,
  Alert,
  Typography,
} from '@mui/material';
import { useAuthStore } from '../../store/auth';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { useLanguageStore } from '../../store/language';
import { verifyTwoFactor } from '../../services/auth/authService';

interface TwoFactorFormProps {
  email: string;
}

const TwoFactorForm: React.FC<TwoFactorFormProps> = ({ email }) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const { currentLanguage } = useLanguageStore();
  const {
    loading,
    error,
    setUser,
    setToken,
    setHasCompleted2FA
  } = useAuthStore();

  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');
  const [verificationError, setVerificationError] = useState('');

  // Check if this is a vendor login

  // Update error messages when language changes
  useEffect(() => {
    // Re-translate error messages when language changes
    if (codeError) {
      setCodeError(t('forms.validation.required'));
    }

    if (verificationError) {
      setVerificationError(t('auth.invalidCode'));
    }
  }, [currentLanguage, i18n.language, t]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCode(e.target.value);
    // Clear both error states when user types
    setCodeError('');
    setVerificationError('');
  };

  const validateForm = () => {
    if (!code.trim()) {
      setCodeError(t('forms.validation.required'));
      return false;
    }
    return true;
  };
const { mutate: verifyMutate, isPending: verifyLoading } = useMutation({
  mutationFn: verifyTwoFactor,
  onSuccess: (response: any) => {
    if (response?.data?.message === '2FA verified successfully' || response?.message === '2FA verified successfully') {
      let userData;
      let userRole = response?.data?.role || response?.role || 'user';
let userId = response?.data.id;
      if (response?.data?.user) {
        userData = response.data.user;
        userData.role = userData.role || userRole;
      } else if (response?.user) {
        userData = response.user;
        userData.role = userData.role || userRole;
      } else {
        userData = {
          id: userId,
          email: email,
          firstName: 'User',
          lastName: 'Name',
          role: userRole,
        };
      } // ✅ Missing closing brace added here

      let tokenData;
      if (response?.data?.token) {
        tokenData = response.data.token;
      } else if (response?.token) {
        tokenData = response.token;
      } else {
        tokenData = `auth-token-${Date.now()}`;
      }

      setUser(userData);
      setToken(tokenData);
      setHasCompleted2FA(true);

      localStorage.setItem(
        'auth-storage',
        JSON.stringify({
          state: {
            token: tokenData,
            user: userData,
            role: userData.role,
            isAuthenticated: true,
            hasCompleted2FA: true,
          },
          version: 0,
        })
      );

      const authStore = useAuthStore.getState();
      authStore.setUser(userData);
      authStore.setToken(tokenData);
      authStore.setHasCompleted2FA(true);

      // Clear vendor login flag after successful verification
      localStorage.removeItem('isVendorLogin');

      toast.success(t('auth.verificationSuccessful'));
      navigate('/dashboard');
      return;
    }

    setVerificationError(t('auth.invalidCode'));
    toast.error(t('auth.verificationFailed'));
  },
  onError: (err: any) => {
    let errorMessage = '';

    if (err.response?.data?.message) {
      errorMessage = err.response.data.message;
    } else if (err.message) {
      errorMessage = err.message;
    } else {
      errorMessage = t('auth.invalidCode');
    }

    setVerificationError(errorMessage);
    toast.error(t('auth.verificationFailed'));
  },
});

  const handleVerifyTwoFactor = (data: any) => {
    verifyMutate(data);
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    setVerificationError('');

    if (!validateForm()) {
      return;
    }
    handleVerifyTwoFactor({ email, code });
  };

  const handleBackToLogin = () => {
    // Clear vendor login flag when going back
    // Navigate to appropriate login page
    navigate('/login');
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
      <Typography variant="body1" sx={{ mb: 2 }}>
        {t('auth.twoFactorDescription')}
        {email && (
          <Box component="span" sx={{ fontWeight: 'bold' }}>
            {` ${email}`}
          </Box>
        )}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {verificationError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {verificationError}
        </Alert>
      )}

      <TextField
        margin="normal"
        required
        fullWidth
        id="code"
        label={t('auth.verificationCode')}
        name="code"
        autoFocus
        value={code}
        onChange={handleChange}
        error={!!codeError}
        helperText={codeError}
        disabled={loading}
        inputProps={{ maxLength: 6 }}
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={loading || verifyLoading}

        sx={{ mt: 3, mb: 2, py: 1.5 }}
      >
        {loading || verifyLoading ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          t('auth.verifyCode')
        )}
      </Button>

      <Button
        fullWidth
        variant="outlined"
        onClick={handleBackToLogin}
        disabled={loading}
        sx={{ mb: 2, py: 1.5 }}
      >
        {t('auth.backToLogin')}
      </Button>
    </Box>
  );
};

export default TwoFactorForm;