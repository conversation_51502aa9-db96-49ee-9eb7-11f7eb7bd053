import api from "../httpClient/api";

export const getAllTimeZones = () => {
  return api.get("/user/getAllTimeZones");
};

export const getAllGlobalSystemPreferences = () =>{
  return api.get("/user/system-preferences");
}
export const getSystemPreferencesHistory = () =>{
  return api.get("/user/view-system-prefs-change-history");
}
export const getCompanyProfileChangeHistory = () =>{
  return api.get("/user/view-compnay-profile-change-history");
}

export const createGlobalSystemPreferences = async (data: Record<string, any>) => {
  try {
    const response = await api.post("/user/create-system-preferences", data); // Axios sets headers automatically
    return response.data;
  } catch (error) {
    console.error("Error creating system preferences:", error);
    throw error;
  }
};



export const uploadLogo = async (formData: FormData) => {
  try {
    const response = await api.post("/user/upload-logo", formData);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};


export const uploadFavicon = async (formData: FormData) => {
  try {
    const response = await api.post("/user/upload-favicon", formData);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};



export const updateGlobalSystemPreferences = async (id: string, data: Record<string, any>) => {
  console.log(data,"data timezomnesss")
  try {
    const response = await api.post(`/user/update-system-preferences`, data);
    return response.data;
  } catch (error) {
    console.error("Error updating system preferences:", error);
    throw error;
  }
};

export const deleteGlobalSystemPreferences = async (id: string) => {
  try {
    const response = await api.delete(`/user/deleteGlobalSystemPreferences/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting system preferences:", error);
    throw error;
  }
};
