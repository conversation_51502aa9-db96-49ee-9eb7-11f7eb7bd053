import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  MenuItem,
  TextField,
  Typography,
  Paper,
  AccordionDetails,
  AccordionSummary,
  Accordion,
} from "@mui/material";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import api from "../../services/httpClient/api";
import VendorStepper from "../../components/steppedbar/VendorStepper";
import { useAuthStore } from "../../store/auth";
import { useSnackbar } from "../../utils/SnackbarProvider";
import { t } from "i18next";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

interface CompanyProfile {
  company_name: string;
  address: string;
  address2: string;
  postal_code: string;
  city: string;
  state_provice: string;
  country: string;
  updated_by: string;
}

const VendorCompanyProfileFullview: React.FC = () => {
  const navigate = useNavigate();
  const params = useParams();
  const [errorMessage, setErrorMessage] = useState("");
  const [imageUrl, setImageUrl] = useState<string>("");
  const { user } = useAuthStore();
  const { id } = params;
  const [currentStep, setCurrentStep] = useState(0);
  const defaultProfile: CompanyProfile = {
    updated_by: String(user?.id) || "",
    company_name: "",
    address: "",
    address2: "",
    postal_code: "",
    city: "",
    state_provice: "",
    country: "",
  };
  const [profile, setProfile] = useState<CompanyProfile>(defaultProfile);
  const [vendor, setVendor] = useState<any>();
  // Extracting the ID from the URL paramss
  useEffect(() => {
    const fetchCompanyProfile = async () => {
      if (!id) return;

      try {
        const res = await api.get(`/user/company-profile-vendor/${id}`);
        const company = res.data?.result[0];
        if (!company || typeof company !== "object") {
          setErrorMessage(
            "Company profile not found. Vendor did not start the registration."
          );
          return;
        }
        setVendor({
          first_name:
            typeof company?.vendor?.first_name === "string"
              ? company.vendor.first_name
              : "",
          last_name:
            typeof company?.vendor?.last_name === "string"
              ? company.vendor.last_name
              : "",
          email:
            typeof company?.vendor?.email === "string"
              ? company.vendor.email
              : "",
        });

        setProfile({
          company_name: company.company_name || "",
          address: company.address || "",
          address2: company.address2 || "",
          postal_code: company.postal_code || "",
          city: company.city || "",
          state_provice: company.state_provice || "",
          country: company.country || "",
          updated_by: String(user?.id) || "",
        });

        setErrorMessage(""); // Clear any previous error
      } catch (error) {
        console.error("Error fetching company profile:", error);
        setErrorMessage("Failed to fetch company profile.");
      }
    };

    fetchCompanyProfile();
  }, [id]);

  const handleChange = (field: keyof CompanyProfile, value: string) => {
    setProfile((prev) => ({ ...prev, [field]: value }));
  };
  const { showMessage } = useSnackbar();

  const handleSubmit = async () => {
    try {
      const response = await api.post(
        `/user/update-company-profile-vendor/${id}`,
        profile
      );
      if (response?.data?.success) {
        showMessage("Company profile saved successfully!", "success");
        setTimeout(() => navigate(`/contact-details/${id}`), 1500);
      } else {
        showMessage(response?.data?.message || "Update failed.", "error");
      }
    } catch (error: any) {
      showMessage(
        error?.response?.data?.message || "An unexpected error occurred.",
        "error"
      );
    }
  };
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [expanded, setExpanded] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setLogoFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!logoFile) {
      showMessage("Please select a logo file to upload.", "warning");
      return;
    }

    const formData = new FormData();
    formData.append("logo", logoFile); // Backend saves this as vendor_logos/logo.png

    try {
      setUploading(true);

      const response = await api.post(
        "/user/company-profile-upload-logo",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (response.data?.success) {
        const uploadedUrl =
          response.data.imageUrl ??
          "https://storageaccountvms01.blob.core.windows.net/vms-assets/vendor_logos/logo.png";

        // Optional: Set to state if needed
        setImageUrl(uploadedUrl); // Make sure `setImageUrl` exists in your component

        showMessage("Logo uploaded successfully!", "success");
      } else {
        showMessage("Upload failed. Please try again.", "error");
      }
    } catch (error) {
      console.error("Error uploading logo:", error);
      showMessage("An error occurred while uploading the logo.", "error");
    } finally {
      setUploading(false);
    }
  };
  const location = useLocation();

  const openChangeHistory = () => {
    navigate("/vendor-profile-change-history", {
      state: { backgroundLocation: location },
    });
  };

  return (
    <>
      <VendorStepper activeStep={currentStep} />
      {errorMessage && (
        <div style={{ color: "red", marginBottom: "1rem" }}>{errorMessage}</div>
      )}
      <Box display="flex" justifyContent="end">
        <Button variant="contained" color="primary" onClick={openChangeHistory}>
          {t("systemPreferences.changeHistory")}
        </Button>
      </Box>
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded(!expanded)}
        sx={{
          backgroundColor: expanded ? "#ffffff" : "#e3f2fd", // Light blue when expanded
          border: "1px solid #ccc",
          borderRadius: 2,
          mt: 2,
          boxShadow: "none",
          "&:before": {
            display: "none",
          },
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="vendor-details-content"
          id="vendor-details-header"
        >
          <Typography variant="h6">Vendor Details</Typography>
        </AccordionSummary>

        <AccordionDetails>
          <Paper
            sx={{ padding: 3, borderRadius: 2, backgroundColor: "#f9f9f9" }}
          >
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="First Name *"
                  value={vendor?.first_name || ""}
                  disabled
                  InputLabelProps={{ shrink: true }}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Last Name *"
                  InputLabelProps={{ shrink: true }}
                  value={vendor?.last_name || ""}
                  fullWidth
                  disabled
                  multiline
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="Email*"
                  InputLabelProps={{ shrink: true }}
                  value={vendor?.email || ""}
                  fullWidth
                  disabled
                  multiline
                />
              </Grid>
            </Grid>
          </Paper>
        </AccordionDetails>
      </Accordion>

      <Box px={3} py={2}>
        <Typography variant="h6" gutterBottom>
          Company Profile
        </Typography>

        <Paper sx={{ padding: 3, borderRadius: 2, backgroundColor: "#f9f9f9" }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Company Name *"
                value={profile.company_name}
                onChange={(e) => handleChange("company_name", e.target.value)}
                fullWidth
                required
              />
            </Grid>

            {/* Logo Upload Placeholder */}
            <Grid item xs={12} sm={6}>
              <Typography>Upload Logo Supplier *</Typography>
              <Box
                sx={{
                  border: "2px dashed #ccc",
                  borderRadius: "8px",
                  padding: 2,
                  textAlign: "center",
                  cursor: "pointer",
                  position: "relative",
                }}
                onClick={() =>
                  document.getElementById("logo-upload-input")?.click()
                }
              >
                {logoFile ? logoFile.name : "Click Here & Upload Logo"}
                <input
                  id="logo-upload-input"
                  type="file"
                  accept="image/png,image/jpeg,image/jpg,image/svg+xml"
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                />
              </Box>

              <Box mt={2}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleUpload}
                  // disabled={uploading}
                  disabled={
                    !profile.company_name &&
                    !profile.address &&
                    !profile.address2 &&
                    !profile.postal_code &&
                    !profile.city &&
                    !profile.state_provice &&
                    !profile.country
                  }
                >
                  {uploading ? "Uploading..." : "Submit"}
                </Button>
                {imageUrl && (
                  <img src={imageUrl} alt="Uploaded Logo" height="80" />
                )}
              </Box>
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Address *"
                value={profile.address}
                onChange={(e) => handleChange("address", e.target.value)}
                fullWidth
                multiline
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Address 2"
                value={profile.address2}
                onChange={(e) => handleChange("address2", e.target.value)}
                fullWidth
                multiline
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Postal Code *"
                value={profile.postal_code}
                onChange={(e) => handleChange("postal_code", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="City *"
                value={profile.city}
                onChange={(e) => handleChange("city", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="State / Province *"
                value={profile.state_provice}
                onChange={(e) => handleChange("state_provice", e.target.value)}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Country *"
                select
                value={profile.country}
                onChange={(e) => handleChange("country", e.target.value)}
                fullWidth
              >
                <MenuItem value="Germany">Germany</MenuItem>
                <MenuItem value="India">India</MenuItem>
                <MenuItem value="USA">USA</MenuItem>
                {/* Add more countries as needed */}
              </TextField>
            </Grid>
          </Grid>
        </Paper>

        <Box display="flex" justifyContent="space-between" mt={3}>
          <Button
  variant="contained"
  onClick={() => {
    navigate("/master-data-management/users?tab=vendor-information");
  }}
>
  Back
</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={
              !profile.company_name &&
              !profile.address &&
              !profile.address2 &&
              !profile.postal_code &&
              !profile.city &&
              !profile.state_provice &&
              !profile.country
            }
          >
            Save & Next
          </Button>
        </Box>
      </Box>
    </>
  );
};

export default VendorCompanyProfileFullview;
