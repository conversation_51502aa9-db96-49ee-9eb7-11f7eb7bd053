import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, Paper, Grid } from '@mui/material';
import LanguageSwitcher from '../layout/LanguageSwitcher';

const TranslationExample: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5">{t('common.settings')}</Typography>
        <LanguageSwitcher />
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Typography variant="h6" gutterBottom>
            {t('auth.title')}
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1">{t('auth.login')}: {t('auth.email')}</Typography>
            <Typography variant="body1">{t('auth.password')}</Typography>
            <Typography variant="body1">{t('auth.rememberMe')}</Typography>
          </Box>
        </Grid>

        <Grid item xs={12} md={6}>
          <Typography variant="h6" gutterBottom>
            {t('dashboard.title')}
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1">{t('dashboard.welcome')}</Typography>
            <Typography variant="body1">{t('dashboard.stats')}</Typography>
            <Typography variant="body1">{t('dashboard.recentActivity')}</Typography>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default TranslationExample;
