import { UseQueryResult } from "@tanstack/react-query";

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  temporaryToken: string | null;
  hasCompleted2FA: boolean;
}

export interface LoginResponse {
  token?: string;
  user?: User;
  temporaryToken?: string;
}

export interface VerifyTwoFactorResponse {
  token: string;
  user: User;
}

export interface LoginFormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface TwoFactorFormValues {
  code: string;
}

export interface ForgotPasswordFormValues {
  email: string;
}

export interface ResetPasswordFormValues {
  password: string;
  confirmPassword: string;
}

export type ThemeMode = "light" | "dark";
export type ResType = UseQueryResult<any, any>;
