import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON>ield,
  Button,
  Box,
  CircularProgress,
  Al<PERSON>,
  Typography,
  <PERSON><PERSON>k<PERSON>,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { toast } from "react-toastify";
import {
  ChangePassword,
  ChangePasswordVendor,
  updatePassword,
  updatePasswordVendor,
} from "../../services/changepassword/changePassword";
import { Visibility, VisibilityOff } from "@mui/icons-material";

interface ChangePasswordFormProps {
  email: string;
}

const VendorChangePasswordForm: React.FC<ChangePasswordFormProps> = ({
  email,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">(
    "success"
  );
  // Form fields
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [twoFactorCode, setTwoFactorCode] = useState("");
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);

  const toggleShowOldPassword = () => setShowOldPassword((show) => !show);
  const toggleShowNewPassword = () => setShowNewPassword((show) => !show);
  const toggleShowConfirmNewPassword = () =>
    setShowConfirmNewPassword((show) => !show);
  // UI states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showTwoFactorField, setShowTwoFactorField] = useState(false);

  // Validation errors
  const [oldPasswordError, setOldPasswordError] = useState<string | null>(null);
  const [newPasswordError, setNewPasswordError] = useState<string | null>(null);
  const [confirmNewPasswordError, setConfirmNewPasswordError] = useState<
    string | null
  >(null);
  const [twoFactorError, setTwoFactorError] = useState<string | null>(null);

  const validateInitialFields = () => {
    let valid = true;
    setOldPasswordError(null);
    setNewPasswordError(null);
    setConfirmNewPasswordError(null);

    if (!oldPassword) {
      setOldPasswordError(t("auth.requiredOldPassword"));
      valid = false;
    }
    if (!newPassword) {
      setNewPasswordError(t("auth.requiredNewPassword"));
      valid = false;
    }
    if (newPassword !== confirmNewPassword) {
      setConfirmNewPasswordError(t("auth.passwordMismatch"));
      valid = false;
    }
    return valid;
  };

  const validateTwoFactorField = () => {
    setTwoFactorError(null);
    if (!twoFactorCode) {
      setTwoFactorError(t("auth.requiredVerificationCode"));
      return false;
    }
    return true;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError(null);
    setLoading(true);

    setTimeout(async () => {
      if (!showTwoFactorField) {
        if (!validateInitialFields()) {
          setLoading(false);
          return;
        }

        try {
          const response = await ChangePasswordVendor({
            password: oldPassword,
            email,
          });
          // ✅ CHECK THIS before proceeding
          if (response?.success) {
            setShowTwoFactorField(true);
            toast.success(t("auth.passwordUpdatedCheckEmail"));
          } else {
            // Don't proceed, show backend error
            setError(response?.message || t("auth.errorChangingPassword"));
          }
        } catch (err: any) {
          console.log(err, "err");
          // Show error from failed request (e.g., 400, 500)
          const message =
            err?.response?.data?.message || t("auth.errorChangingPassword");
          setError(message);

          // ✅ EXTRA SAFETY: DO NOT show two-factor field on failure
          setShowTwoFactorField(false);
        } finally {
          setLoading(false);
        }
      } else {
        if (!validateTwoFactorField()) {
          setLoading(false);
          return;
        }

        try {
          await updatePasswordVendor({
            code: twoFactorCode,
            email,
            password: newPassword,
          });

          setSnackbarMessage(t("auth.credentialsChangedSuccess"));
          setSnackbarSeverity("success");
          setSnackbarOpen(true);
          setLoading(false);

          setTimeout(() => {
            navigate("/vendor-login");
          }, 3000);
        } catch (err: any) {
          setTwoFactorError(
            err?.response?.data?.message || t("auth.invalidVerificationCode")
          );
          setLoading(false);
        }
      }
    }, 5000);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <MuiAlert severity={snackbarSeverity} sx={{ width: "100%" }}>
          {snackbarMessage}
        </MuiAlert>
      </Snackbar>
      {!showTwoFactorField ? (
        <>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {t("auth.changePasswordFor")}
            {email && (
              <Box component="span" sx={{ fontWeight: "bold" }}>
                {` ${email}`}
              </Box>
            )}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TextField
            margin="normal"
            required
            fullWidth
            id="oldPassword"
            label={t("auth.oldPassword")}
            name="oldPassword"
            autoComplete="current-password"
            value={oldPassword}
            onChange={(e) => setOldPassword(e.target.value)}
            error={!!oldPasswordError}
            helperText={oldPasswordError}
            disabled={loading}
            type={showOldPassword ? "text" : "password"}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={toggleShowOldPassword}
                    edge="end"
                    tabIndex={-1}
                  >
                    {showOldPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <TextField
            margin="normal"
            required
            fullWidth
            id="newPassword"
            label={t("auth.newPassword")}
            name="newPassword"
            autoComplete="new-password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            error={!!newPasswordError}
            helperText={newPasswordError}
            disabled={loading}
            type={showNewPassword ? "text" : "password"}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={toggleShowNewPassword}
                    edge="end"
                    tabIndex={-1}
                  >
                    {showNewPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <TextField
            margin="normal"
            required
            fullWidth
            id="confirmNewPassword"
            label={t("auth.confirmNewPassword")}
            name="confirmNewPassword"
            autoComplete="new-password"
            value={confirmNewPassword}
            onChange={(e) => setConfirmNewPassword(e.target.value)}
            error={!!confirmNewPasswordError}
            helperText={confirmNewPasswordError}
            disabled={loading}
            type={showConfirmNewPassword ? "text" : "password"}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={toggleShowConfirmNewPassword}
                    edge="end"
                    tabIndex={-1}
                  >
                    {showConfirmNewPassword ? (
                      <VisibilityOff />
                    ) : (
                      <Visibility />
                    )}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </>
      ) : (
        <>
          <Typography variant="body2" sx={{ mb: 2, color: "text.secondary" }}>
            {t("auth.enterVerificationCodeSentToMail")}
          </Typography>

          <TextField
            margin="normal"
            required
            fullWidth
            id="twoFactorCode"
            label={t("auth.verificationCode")}
            name="twoFactorCode"
            autoFocus
            value={twoFactorCode}
            onChange={(e) => setTwoFactorCode(e.target.value)}
            error={!!twoFactorError}
            helperText={twoFactorError}
            disabled={loading}
            inputProps={{ maxLength: 6 }}
          />
        </>
      )}

      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={loading}
        sx={{ mt: 3, mb: 2, py: 1.5 }}
      >
        {loading ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          t("auth.submit")
        )}
      </Button>
    </Box>
  );
};

export default VendorChangePasswordForm;
