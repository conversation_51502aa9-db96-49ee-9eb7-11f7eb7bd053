import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { resetPassword, resetPasswordVendor } from '../../services/auth/authService';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { useLanguageStore } from '../../store/language';

interface ResetPasswordFormProps {
  email: string;
  token: string;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ email, token }) => {
  const { t, i18n } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const navigate = useNavigate();

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Store error types for re-translation
  const [passwordErrorType, setPasswordErrorType] = useState<'required' | 'minLength' | ''>('');
  const [confirmPasswordErrorType, setConfirmPasswordErrorType] = useState<'required' | 'confirmPassword' | ''>('');

  // Update error messages when language changes
  useEffect(() => {
    if (passwordError && passwordErrorType) {
      if (passwordErrorType === 'required') {
        setPasswordError(t('forms.validation.required'));
      } else if (passwordErrorType === 'minLength') {
        setPasswordError(t('forms.validation.minLength', { min: 8 }));
      }
    }

    if (confirmPasswordError && confirmPasswordErrorType) {
      if (confirmPasswordErrorType === 'required') {
        setConfirmPasswordError(t('forms.validation.required'));
      } else if (confirmPasswordErrorType === 'confirmPassword') {
        setConfirmPasswordError(t('forms.validation.confirmPassword'));
      }
    }
  }, [currentLanguage, i18n.language, t, passwordError, passwordErrorType, confirmPasswordError, confirmPasswordErrorType]);

  const { mutate, isPending, error } = useMutation({
    mutationFn: () =>resetPassword(email, token, password),
    onSuccess: (response: any) => {
      setSuccess(true);
      toast.success(t('auth.passwordResetSuccess'));

      localStorage.removeItem('resetPasswordEmail');

      setTimeout(() => {
        navigate('/login');
      }, 3000);
    },
    onError: (err: any) => {
      toast.error(err.message || t('auth.somethingWentWrong'));
    }
  });

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    setPasswordError('');
    setPasswordErrorType('');

    if (confirmPassword && e.target.value === confirmPassword) {
      setConfirmPasswordError('');
      setConfirmPasswordErrorType('');
    }
  };

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.target.value);
    setConfirmPasswordError('');
    setConfirmPasswordErrorType('');
  };

  const validateForm = () => {
    let isValid = true;

    if (!password) {
      setPasswordError(t('forms.validation.required'));
      setPasswordErrorType('required');
      isValid = false;
    } else if (password.length < 8) {
      setPasswordError(t('forms.validation.minLength', { min: 8 }));
      setPasswordErrorType('minLength');
      isValid = false;
    }

    if (!confirmPassword) {
      setConfirmPasswordError(t('forms.validation.required'));
      setConfirmPasswordErrorType('required');
      isValid = false;
    } else if (password !== confirmPassword) {
      setConfirmPasswordError(t('forms.validation.confirmPassword'));
      setConfirmPasswordErrorType('confirmPassword');
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }
    mutate();
  };

  const handleBackToLogin = () => {
    navigate('/login');
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  if (success) {
    return (
      <Box sx={{ mt: 1, textAlign: 'center' }}>
        <Alert severity="success" sx={{ mb: 2 }}>
          {t('auth.passwordResetSuccess')}
        </Alert>

        <Typography variant="body1" sx={{ mb: 2 }}>
          {t('auth.redirectingToLogin')}
        </Typography>

        <Button
          fullWidth
          variant="contained"
          onClick={handleBackToLogin}
          sx={{ mt: 2, py: 1.5 }}
        >
          {t('auth.backToLogin')}
        </Button>
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
      <Typography variant="body1" sx={{ mb: 2 }}>
        {t('auth.resetPasswordDescription')}
      </Typography>

      {email && (
        <Typography variant="body2" sx={{ mb: 2, fontWeight: 'bold' }}>
          {t('auth.resettingPasswordFor')}: {email}
        </Typography>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error.message}
        </Alert>
      )}

      <TextField
        margin="normal"
        required
        fullWidth
        name="password"
        label={t('auth.newPassword')}
        type={showPassword ? 'text' : 'password'}
        id="password"
        autoComplete="new-password"
        value={password}
        onChange={handlePasswordChange}
        error={!!passwordError}
        helperText={passwordError}
        disabled={isPending}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={toggleShowPassword} edge="end">
                {showPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      <TextField
        margin="normal"
        required
        fullWidth
        name="confirmPassword"
        label={t('auth.confirmPassword')}
        type={showConfirmPassword ? 'text' : 'password'}
        id="confirmPassword"
        autoComplete="new-password"
        value={confirmPassword}
        onChange={handleConfirmPasswordChange}
        error={!!confirmPasswordError}
        helperText={confirmPasswordError}
        disabled={isPending}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={toggleShowConfirmPassword} edge="end">
                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={isPending}
        sx={{ mt: 3, mb: 2, py: 1.5 }}
      >
        {isPending ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          t('auth.resetPassword')
        )}
      </Button>

      <Button
        fullWidth
        variant="outlined"
        onClick={handleBackToLogin}
        disabled={isPending}
        sx={{ mb: 2, py: 1.5 }}
      >
        {t('auth.backToLogin')}
      </Button>
    </Box>
  );
};

export default ResetPasswordForm;