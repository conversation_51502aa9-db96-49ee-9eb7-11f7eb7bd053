import { useEffect, useRef } from 'react';
import { useAuthStore } from '../store/auth';

const ACTIVITY_TIMEOUT_MS = 30 * 60 * 1000; 

export const useSessionTimeout = () => {
  const { isAuthenticated, logout } = useAuthStore();
  const timeoutRef = useRef<number | null>(null);
  const showingDialogRef = useRef(false);

  const resetTimeout = () => {
    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
    }
    
    if (isAuthenticated && !showingDialogRef.current) {
      timeoutRef.current = window.setTimeout(() => {
        showingDialogRef.current = true;
                logout();
                showingDialogRef.current = false;
      }, ACTIVITY_TIMEOUT_MS);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      return;
    }

    resetTimeout();

    // Set up event listeners for user activity
    const activityEvents = [
      'mousedown',
      'keydown',
      'touchstart',
      'click',
      'mousemove',
      'scroll'
    ];

    const handleUserActivity = () => {
      resetTimeout();
    };

    activityEvents.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });

    return () => {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }
      
      activityEvents.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });
    };
  }, [isAuthenticated, logout]);

  return null;
};