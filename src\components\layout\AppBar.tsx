import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  AppBar as MuiAppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  MenuItem,
  Avatar,
  Tooltip,
  useTheme,
  styled,
  AppBarProps as MuiAppBarProps,
} from "@mui/material";
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  Person as PersonIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/auth";
import LanguageSwitcher from "./LanguageSwitcher";
import { useQuery } from "@tanstack/react-query";
import { getAllvendors } from "../../services/vendor/vendor";

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
  drawerWidth: number;
}

const StyledAppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open" && prop !== "drawerWidth",
})<AppBarProps>(({ theme, open, drawerWidth }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

interface Props {
  open: boolean;
  toggleDrawer: () => void;
  drawerWidth: number;
}

const AppBar: React.FC<Props> = ({ open, toggleDrawer, drawerWidth }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);

  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const { data: existingPreferences } = useQuery({
    queryKey: ["getAllvendors"],
    queryFn: async () => {
      const response = await getAllvendors();
      return response.result;
    },
  });
const handleChangePassword = () => {
  navigate(user?.role === "vendor" ? "/vendor-change-password" : "/change-password");
};

  const vendorName =
    existingPreferences && Array.isArray(existingPreferences)
      ? existingPreferences.find(
          (vendor: {
            id: number;
            name: string;
            email: string;
            roles: string[];
          }) => {
            return (
              vendor.email?.toLowerCase().trim() ===
              user?.email?.toLowerCase().trim()
            );
          }
        )?.name || "Admin"
      : "Loading...";

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleCloseMenu();
    logout();
    navigate("/login");
  };

  return (
    <StyledAppBar position="fixed" open={open} drawerWidth={drawerWidth}>
      <Toolbar>
        <IconButton
          color="inherit"
          aria-label="toggle drawer"
          edge="start"
          onClick={toggleDrawer}
          sx={{
            marginRight: 2,
          }}
        >
          {open ? <ChevronLeftIcon /> : <MenuIcon />}
        </IconButton>

        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
          Admin Dashboard
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ mr: 2 }}>
            <LanguageSwitcher variant="icon" />
          </Box>

          <Tooltip title={vendorName || user?.firstName || t("common.profile")}>
            <IconButton
              onClick={handleOpenMenu}
              size="small"
              edge="end"
              aria-controls={menuOpen ? "account-menu" : undefined}
              aria-haspopup="true"
              aria-expanded={menuOpen ? "true" : undefined}
              sx={{ ml: 1 }}
            >
              <Avatar
                sx={{
                  bgcolor: theme.palette.primary.main,
                  width: 32,
                  height: 32,
                }}
              >
                {vendorName?.charAt(0) || user?.firstName?.charAt(0) || (
                  <PersonIcon />
                )}
              </Avatar>
            </IconButton>
          </Tooltip>
        </Box>

        <Menu
          id="account-menu"
          anchorEl={anchorEl}
          open={menuOpen}
          onClose={handleCloseMenu}
          MenuListProps={{
            "aria-labelledby": "profile-button",
          }}
          PaperProps={{
            elevation: 0,
            sx: {
              overflow: "visible",
              filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.1))",
              mt: 1.5,
              "& .MuiAvatar-root": {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
            },
          }}
          transformOrigin={{ horizontal: "right", vertical: "top" }}
          anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
        >
          <MenuItem onClick={handleCloseMenu}>
            <Typography>{t("common.profile")}</Typography>
          </MenuItem>
              <MenuItem onClick={handleChangePassword}>
            <Typography>{t("common.changePassword")}</Typography>
          </MenuItem>
          <MenuItem onClick={handleLogout}>
            <Typography color="error">{t("common.logout")}</Typography>
          </MenuItem>
        </Menu>
      </Toolbar>
    </StyledAppBar>
  );
};

export default AppBar;
