import React, { useState, useEffect, useMemo } from "react";
import { Add, Edit, Delete } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  IconButton,
  Box,
} from "@mui/material";
import _ from "lodash";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import { useNavigate } from "react-router-dom";
import useQueryParams from "../../hooks/useQueryParams";
import Loader from "../../utils/Loader";
import { getAllGlobalSystemPreferences } from "../../services/preferences/preferences";
import { StyledProfileNav, StyledProfileNavItem } from "../roles/styles";
import { createPermission, deletePermission, getAllPermissions, Permission, updatePermission } from "../../services/permissions/permissions";

const permissionTabs = [
  {
    titleKey: "permissions.userPermissions",
    path: "user-permissions",
    userType: "business" as const,
  },
  {
    titleKey: "permissions.vendorPermissions",
    path: "vendor-permissions",
    userType: "vendor" as const,
  },
];

function PermissionsPage() {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [page, setPage] = useState<number>(0);
  const [pageCount, setPageCount] = useState<number>(10);
  const [sort, setSort] = useState<any>({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">(
    "success"
  );
  const { user } = useAuthStore();
  const { queryParams } = useQueryParams();
  const active: any = queryParams.tab;

  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Permission | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Permission | null>(null);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
  });

  const [formErrors, setFormErrors] = useState({
    name: "",
    description: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!active) {
      navigate("?tab=user-permissions", { replace: true });
    }
  }, [active, navigate]);

  const currentTab =
    permissionTabs.find((tab) => tab.path === active) || permissionTabs[0];

  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });

  const { data: permissionsResponse, isLoading } = useQuery({
    queryKey: ["permissions", currentTab.userType, page, pageCount, sort],
    queryFn: async () => {
      const response = await getAllPermissions();
      const allRoles = response.result || [];
      // Filter roles based on current tab's userType
      const filteredRoles = allRoles.filter(
        (role: any) => role.userType === currentTab.userType
      );
      return {
        result: filteredRoles,
        totalCount: filteredRoles.length
      };
    },
  });

  const rolesData = permissionsResponse?.result || [];
  const totalCount = permissionsResponse?.totalCount || 0;
  const defaultColumns: Array<ColumnType> = useMemo(
    () => [
      {
        title: t("permissions.permssionName"),
        key: "permission_name",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("permissions.permissionDescription"),
        key: "description",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("common.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        render: (row: Permission) => (
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => handleEdit(row)}
              aria-label="Edit role"
            >
              <Edit />
            </IconButton>
            {/* <IconButton
              size="small"
              onClick={() => handleDeleteClick(row)}
              aria-label="Delete role"
              color="error"
            >
              <Delete />
            </IconButton> */}
          </Box>
        ),
      },
    ],
    [t, currentLanguage, preferences]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: "",
      });
    }

    if (generalError) {
      setGeneralError("");
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formData.name.trim()) {
      newErrors.name = t("permissions.permissionNameRequired");
      valid = false;
    } else {
      newErrors.name = "";
    }

    if (!formData.description.trim()) {
      newErrors.description = t("permissions.permissionDescriptionRequired");
      valid = false;
    } else {
      newErrors.description = "";
    }

    setFormErrors(newErrors);
    return valid;
  };

  const createRoleMutation = useMutation({
    mutationFn: createPermission,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      setOpen(false);
      setSnackbarMessage(t("permissions.permissionsCreatedSuccess"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      resetForm();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || t("common.unknownError");
      setGeneralError(message);
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Permission> }) =>
      updatePermission(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      setOpen(false);
      setSnackbarMessage(t("permissions.permissionUpdatedSuccess"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      resetForm();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || t("common.unknownError");
      setGeneralError(message);
    },
  });

 const deleteRoleMutation = useMutation({
  mutationFn: async (id: number) => await deletePermission(id),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ["permissions"] }); // was "roles"
    setDeleteDialogOpen(false);
    setSnackbarMessage(t("permissions.permission"));
    setSnackbarSeverity("success");
    setSnackbarOpen(true);
    setRoleToDelete(null);
  },
  onError: (error: any) => {
    const message = error.response?.data?.message || t("common.unknownError");
    setSnackbarMessage(message);
    setSnackbarSeverity("error");
    setSnackbarOpen(true);
  },
});


  const handleSubmit = async () => {
    setGeneralError("");

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const payload: any = {
        permission_name: formData.name,
        description: formData.description,
        userType: currentTab.userType,
      };

      if (editMode && selectedRole) {
        updateRoleMutation.mutate({ id: selectedRole.id!, data: payload });
      } else {
        createRoleMutation.mutate(payload);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (permission: Permission) => {
    setSelectedRole(permission);
    setFormData({
      name: permission.permission_name,
      description: permission.description,
    });
    setEditMode(true);
    setOpen(true);
    setGeneralError("");
    setFormErrors({ name: "", description: "" });
  };

const handleDeleteClick = (permission: Permission) => {
  setRoleToDelete(permission);
  setDeleteDialogOpen(true);
};

const handleDeleteConfirm = () => {
  if (roleToDelete && !deleteRoleMutation.isLoading) {
    deleteRoleMutation.mutate(roleToDelete.id!);
  }
};


  const resetForm = () => {
    setFormData({ name: "", description: "" });
    setFormErrors({ name: "", description: "" });
    setGeneralError("");
    setEditMode(false);
    setSelectedRole(null);
  };

  const handleCreateNew = () => {
    resetForm();
    setOpen(true);
  };

  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      {/* Tab Navigation */}
      <Box sx={{ position: "relative", bgcolor: "white", zIndex: 2 }}>
        <StyledProfileNav>
          {permissionTabs.map((item, index) => (
            <StyledProfileNavItem
              key={index}
              onClick={() => navigate(`?tab=${item.path}`)}
              active={active === item.path ? 1 : 0}
            >
              {t(item.titleKey)}
            </StyledProfileNavItem>
          ))}
        </StyledProfileNav>
      </Box>

      <Box m={2}>
        <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateNew}
          >
            {t("permissions.createPermission")}
          </Button>
        </Box>

        <Table
         sx={{height:'auto'}}
          columns={columns}
          data={rolesData || []}
          sortHandler={handleSort}
          pagination={{
            totalCount,
            pageCount,
            setPageCount,
            page,
            setPage,
          }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />
      </Box>

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {editMode ? t("permissions.editPermission") : t("permissions.createPermission")}
        </DialogTitle>
        <DialogContent dividers>
          {generalError && <Alert severity="error">{generalError}</Alert>}

          <TextField
            label={t("permissions.permissionName")}
            variant="outlined"
            fullWidth
            margin="normal"
            name="name" 
            value={formData.name}
            onChange={handleChange}
            error={!!formErrors.name}
            helperText={formErrors.name}
            autoFocus
          />
          <TextField
            label={t("permissions.permissionDescription")}
            variant="outlined"
            fullWidth
            margin="normal"
            name="description"
            value={formData.description}
            onChange={handleChange}
            error={!!formErrors.description}
            helperText={formErrors.description}
            multiline
            minRows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>{t("common.cancel")}</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting}
          >
            {editMode ? t("common.update") : t("common.create")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
     <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
  <DialogTitle>{t("permissions.confirmDeleteTitle")}</DialogTitle>
  <DialogContent>
    <Typography>
      {t("permissions.confirmDeleteMessage", {
        name: roleToDelete?.permission_name || "",
      })}
    </Typography>
  </DialogContent>
  <DialogActions>
    <Button onClick={() => setDeleteDialogOpen(false)} color="primary">
      {t("common.cancel")}
    </Button>
    <Button
      onClick={handleDeleteConfirm}
      color="error"
      variant="contained"
      disabled={deleteRoleMutation.isLoading}
    >
      {deleteRoleMutation.isLoading
        ? t("common.deleting") + "..."
        : t("common.delete")}
    </Button>
  </DialogActions>
</Dialog>


      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <MuiAlert severity={snackbarSeverity} sx={{ width: "100%" }}>
          {snackbarMessage}
        </MuiAlert>
      </Snackbar>
    </>
  );
}

export default PermissionsPage;
