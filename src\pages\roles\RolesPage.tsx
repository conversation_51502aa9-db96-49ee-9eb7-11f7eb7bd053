import React, { useState, useEffect, useMemo } from "react";
import { Add, Edit, Delete } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  TextField,
  Typography,
  Alert,
  IconButton,
  Box,
} from "@mui/material";
import _ from "lodash";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Table, { ColumnType } from "../../components/Table";
import sortHandler from "../../components/Table/sortHandler";
import {
  getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  Role,
} from "../../services/roles/roles";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../../store/auth";
import { useLanguageStore } from "../../store/language";
import { useNavigate } from "react-router-dom";
import useQueryParams from "../../hooks/useQueryParams";
import Loader from "../../utils/Loader";
import { formatDate } from "../../utils/dateFormats";
import { getAllGlobalSystemPreferences } from "../../services/preferences/preferences";
import { StyledProfileNav, StyledProfileNavItem } from "./styles";

const rolesTabs = [
  {
    titleKey: "roles.userRoles",
    path: "user-roles",
    userType: "business" as const,
  },
  {
    titleKey: "roles.vendorRoles",
    path: "vendor-roles",
    userType: "vendor" as const,
  },
];

function RolesPage() {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguageStore();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [page, setPage] = useState<number>(0);
  const [pageCount, setPageCount] = useState<number>(10);
  const [sort, setSort] = useState<any>({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">(
    "success"
  );
  const { user } = useAuthStore();
  const { queryParams } = useQueryParams();
  const active: any = queryParams.tab;

  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);

  const [formData, setFormData] = useState({
    role_name: "",
    description: "",
  });

  const [formErrors, setFormErrors] = useState({
    role_name: "",
    description: "",
  });

  const [generalError, setGeneralError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!active) {
      navigate("?tab=user-roles", { replace: true });
    }
  }, [active, navigate]);

  const currentTab =
    rolesTabs.find((tab) => tab.path === active) || rolesTabs[0];

  const { data: preferences } = useQuery({
    queryKey: ["globalSystemPreferences"],
    queryFn: async () => (await getAllGlobalSystemPreferences()).data,
  });

  const { data: rolesResponse, isLoading } = useQuery({
    queryKey: ["roles", currentTab.userType, page, pageCount, sort],
    queryFn: async () => {
      const response = await getAllRoles();
      const allRoles = response.result || [];
      // Filter roles based on current tab's userType
      const filteredRoles = allRoles.filter(
        (role: any) => role.userType === currentTab.userType
      );
      return {
        result: filteredRoles,
        totalCount: filteredRoles.length
      };
    },
  });

  const rolesData = rolesResponse?.result || [];
  const totalCount = rolesResponse?.totalCount || 0;
  const defaultColumns: Array<ColumnType> = useMemo(
    () => [
      {
        title: t("roles.roleName"),
        key: "role_name",
        sort: true,
        searchable: true,
        filterable: true,
      },
      {
        title: t("roles.roleDescription"),
        key: "description",
        sort: true,
        searchable: true,
        filterable: true,
      },
      // {
      //   title: t("common.createdAt"),
      //   key: "createdAt",
      //   sort: true,
      //   filterable: true,
      //   render: (row) => {
      //     const dateFormat =
      //       preferences?.result[0]?.date_format || "YYYY-MM-DD";
      //     return <span>{formatDate(row.createdAt, dateFormat)}</span>;
      //   },
      // },
      {
        title: t("common.actions"),
        key: "actions",
        searchable: false,
        filterable: false,
        render: (row: Role) => (
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              size="small"
              onClick={() => handleEdit(row)}
              aria-label="Edit role"
            >
              <Edit />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => handleDeleteClick(row)}
              aria-label="Delete role"
              color="error"
            >
              <Delete />
            </IconButton>
          </Box>
        ),
      },
    ],
    [t, currentLanguage, preferences]
  );

  const [columns, setColumns] = useState(_.cloneDeep(defaultColumns));

  useEffect(() => {
    setColumns(_.cloneDeep(defaultColumns));
  }, [defaultColumns]);

  const handleSort = (v: any) => {
    sortHandler({
      key: v.key,
      columns,
      sortState: sort,
      setSortState: setSort,
      setColumns: setColumns,
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: "",
      });
    }

    if (generalError) {
      setGeneralError("");
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formData.role_name.trim()) {
      newErrors.role_name = t("roles.roleNameRequired");
      valid = false;
    } else {
      newErrors.role_name = "";
    }

    if (!formData.description.trim()) {
      newErrors.description = t("roles.roleDescriptionRequired");
      valid = false;
    } else {
      newErrors.description = "";
    }

    setFormErrors(newErrors);
    return valid;
  };

  const createRoleMutation = useMutation({
    mutationFn: createRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setOpen(false);
      setSnackbarMessage(t("roles.roleCreated"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      resetForm();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || t("common.unknownError");
      setGeneralError(message);
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<Role> }) =>
      updateRole(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setOpen(false);
      setSnackbarMessage(t("roles.roleUpdated"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      resetForm();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || t("common.unknownError");
      setGeneralError(message);
    },
  });

  const deleteRoleMutation = useMutation({
    mutationFn: deleteRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      setDeleteDialogOpen(false);
      setSnackbarMessage(t("roles.roleDeleted"));
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      setRoleToDelete(null);
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || t("common.unknownError");
      setSnackbarMessage(message);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    },
  });

  const handleSubmit = async () => {
    setGeneralError("");

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const payload: any = {
        role_name: formData.role_name,
        description: formData.description,
        userType: currentTab.userType,
      };

      if (editMode && selectedRole) {
        updateRoleMutation.mutate({ id: selectedRole.id!, data: payload });
      } else {
        createRoleMutation.mutate(payload);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (role: Role) => {
    setSelectedRole(role);
    setFormData({
      role_name: role.role_name,
      description: role.description,
    });
    setEditMode(true);
    setOpen(true);
    setGeneralError("");
    setFormErrors({ role_name: "", description: "" });
  };

  const handleDeleteClick = (role: Role) => {
    setRoleToDelete(role);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (roleToDelete) {
      deleteRoleMutation.mutate(roleToDelete.id!);
    }
  };

  const resetForm = () => {
    setFormData({ role_name: "", description: "" });
    setFormErrors({ role_name: "", description: "" });
    setGeneralError("");
    setEditMode(false);
    setSelectedRole(null);
  };

  const handleCreateNew = () => {
    resetForm();
    setOpen(true);
  };

  if (isLoading) return <Loader value={"Loading..."} />;

  if (user?.role !== "business") {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error">
          {t("systemPreferences.permissionDenied")}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      {/* Tab Navigation */}
      <Box sx={{ position: "relative", bgcolor: "white", zIndex: 2 }}>
        <StyledProfileNav>
          {rolesTabs.map((item, index) => (
            <StyledProfileNavItem
              key={index}
              onClick={() => navigate(`?tab=${item.path}`)}
              active={active === item.path ? 1 : 0}
            >
              {t(item.titleKey)}
            </StyledProfileNavItem>
          ))}
        </StyledProfileNav>
      </Box>

      <Box m={2}>
        <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateNew}
          >
            {t("roles.createNew")}
          </Button>
        </Box>

        <Table
         sx={{height:'auto'}}
          columns={columns}
          data={rolesData || []}
          sortHandler={handleSort}
          pagination={{
            totalCount,
            pageCount,
            setPageCount,
            page,
            setPage,
          }}
          enableSearch={true}
          enableFilter={true}
          searchPlaceholder={t("table.searchPlaceholder")}
          loading={isLoading}
        />
      </Box>

      {/* Role Create/Edit Dialog */}
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {editMode ? t("roles.editRole") : t("roles.createRole")}
        </DialogTitle>
        <DialogContent dividers>
          {generalError && <Alert severity="error">{generalError}</Alert>}

          <TextField
            label={t("roles.roleName")}
            variant="outlined"
            fullWidth
            margin="normal"
            name="role_name" // FIX: set correct name here
            value={formData.role_name}
            onChange={handleChange}
            error={!!formErrors.role_name}
            helperText={formErrors.role_name}
            autoFocus
          />
          <TextField
            label={t("roles.roleDescription")}
            variant="outlined"
            fullWidth
            margin="normal"
            name="description"
            value={formData.description}
            onChange={handleChange}
            error={!!formErrors.description}
            helperText={formErrors.description}
            multiline
            minRows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>{t("common.cancel")}</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting}
          >
            {editMode ? t("common.update") : t("common.create")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t("roles.deleteRole")}</DialogTitle>
        <DialogContent>
          <Typography>{t("roles.confirmDeleteRole")}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t("common.cancel")}
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
          >
            {t("common.delete")}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <MuiAlert severity={snackbarSeverity} sx={{ width: "100%" }}>
          {snackbarMessage}
        </MuiAlert>
      </Snackbar>
    </>
  );
}

export default RolesPage;
