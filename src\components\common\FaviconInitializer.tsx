import { useEffect } from 'react';
import { useSystemPreferencesStore } from '../../store/systemPreferences';
import { useReactiveFavicon } from '../../hooks/useSystemPreferences';
import { getAllGlobalSystemPreferences } from '../../services/preferences/preferences';

const FaviconInitializer: React.FC = () => {
  const { setPreferences, setLogoUrl, setFaviconUrl } = useSystemPreferencesStore();
  const currentFaviconUrl = useReactiveFavicon(); // This will automatically update favicon

  useEffect(() => {
    const initializePreferences = async () => {
      try {
        const response = await getAllGlobalSystemPreferences();

        if (response?.data?.result?.length > 0) {
          const preferences = response.data.result[0];
          setPreferences(preferences);

          if (preferences.logoUrl) {
            setLogoUrl(preferences.logoUrl);
          }

          if (preferences.faviconUrl) {
            setFaviconUrl(preferences.faviconUrl);
          }
        } else {
          console.log('FaviconInitializer: No preferences found in response');
        }
      } catch (error) {
        console.error('FaviconInitializer: Failed to initialize system preferences:', error);
      }
    };

    initializePreferences();
  }, [setPreferences, setLogoUrl, setFaviconUrl]);

  // Debug logging for favicon
  useEffect(() => {
    console.log('FaviconInitializer: Current favicon URL:', currentFaviconUrl);
  }, [currentFaviconUrl]);

  return null; // This component doesn't render anything
};

export default FaviconInitializer;
