import { <PERSON><PERSON><PERSON>, <PERSON>, TextField, Grid, But<PERSON> } from "@mui/material";
import { getAllvendors } from "../../services/vendor/vendor";
import { useQuery } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router-dom";
import { StyledProfileNav, StyledProfileNavItem } from "../users/styles";
import { vendorMenu } from "../../data/constants";
import SampleData2 from "../users/sampleData2";
import SampleData3 from "../users/sampleData3";
import NoData from "../users/Nodata";
import useQueryParams from "../../hooks/useQueryParams";
import { useTranslation } from "react-i18next";

const VendorInformation = () => {
  const { t } = useTranslation();
  const { id } = useParams(); // Get vendor ID from URL param
  const navigate = useNavigate();
  const { queryParams } = useQueryParams();

const active: any = queryParams.tab || "vendor-information";

  const { data: existingPreferences, isLoading } = useQuery({
    queryKey: ["getAllvendors"],
    queryFn: async () => {
      const response = await getAllvendors();
      return response.result;
    },
  });


  const matchedVendor = Array.isArray(existingPreferences)
    ? existingPreferences.find((vendor: any) => String(vendor.id) === String(id))
    : null;

  const vendorName = matchedVendor?.first_name || "N/A";
  const vendorLastName = matchedVendor?.last_name || "N/A";
  const vendorEmail = matchedVendor?.email || "N/A";
  if (isLoading) return <Typography>Loading...</Typography>;

  return (
    <Box sx={{ position: "relative", bgcolor: "white", zIndex: 2 }}>
      <StyledProfileNav>
        {vendorMenu.map((item, index) => (
          <StyledProfileNavItem
            key={index}
            onClick={() => navigate(`?tab=${item.path}`)}
            active={active === item.path ? 1 : 0}
          >
            {t(item.titleKey)}
          </StyledProfileNavItem>
        ))}
      </StyledProfileNav>

      <Box px={2} pl={1} pr={1} pt={3}>
        {active === "vendor-information" && (
          <Box>
            <Typography variant="h5" gutterBottom>
              {t("vendor.basicInformation")}
            </Typography>

            <Grid container spacing={2} sx={{ mt: 2 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("users.firstName")}
                  value={vendorName}
                  fullWidth
                  InputProps={{
                    readOnly: true,
                  }}
                  variant="outlined"
                />
              </Grid>
               <Grid item xs={12} sm={6}>
                <TextField
                  label={t("users.lastName")}
                  value={vendorLastName}
                  fullWidth
                  InputProps={{
                    readOnly: true,
                  }}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("users.email")}
                  value={vendorEmail}
                  fullWidth
                  InputProps={{
                    readOnly: true,
                  }}
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </Box>
        )}
         <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 2,
                  px: 3,
                  mt: 2,
                }}
              >
                <Button variant="outlined" color="primary" onClick={() => navigate(-1)}>
                  {t("common.back")}
                </Button>

              </Box>

        {active === "no-data" && <NoData />}
        {active === "sample-data-2" && <SampleData2 />}
        {active === "sample-data-3" && <SampleData3 />}
      </Box>
    </Box>
  );
};

export default VendorInformation;
