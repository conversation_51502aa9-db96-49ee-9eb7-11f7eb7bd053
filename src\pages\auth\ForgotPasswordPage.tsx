import React from "react";
import { useTranslation } from "react-i18next";
import { Navigate } from "react-router-dom";
import { CardContent, Container, Typography, Box, Paper } from "@mui/material";
import { useAuthStore } from "../../store/auth";
import ForgotPasswordForm from "../../components/auth/ForgotPasswordForm";
import LanguageSwitcher from "../../components/layout/LanguageSwitcher";
import { useReactiveLogo } from "../../hooks/useSystemPreferences";

const ForgotPasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuthStore();
  const reactiveLogo = useReactiveLogo();
  const BASE_URL = import.meta.env.VITE_ASSETS_BASE_URL || '';

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: (theme) => theme.palette.grey[50],
      }}
    >
      <Container maxWidth="sm">
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            mb: 2,
          }}
        >
          <LanguageSwitcher />
        </Box>

        <Paper
          elevation={6}
          sx={{
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              bgcolor: "primary.main",
              py: 3,
              px: 4,
              color: "white",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Box
              component="img"
              src={reactiveLogo}
              alt="Logo"
              sx={{
                height: 48,
                mb: 2,
                objectFit: 'contain',
                maxWidth: '100%'
              }}
              onError={(e) => {
                console.error('Logo failed to load:', reactiveLogo);
                // Fallback to default logo
                const target = e.target as HTMLImageElement;
                target.src = `${BASE_URL}logo.png`;
              }}
            />
            <Typography variant="h4" component="h1" gutterBottom>
              {t("auth.forgotPasswordTitle")}
            </Typography>
          </Box>

          <CardContent sx={{ py: 4, px: { xs: 3, sm: 4 } }}>
            <ForgotPasswordForm />
          </CardContent>
        </Paper>
      </Container>
    </Box>
  );
};

export default ForgotPasswordPage;
