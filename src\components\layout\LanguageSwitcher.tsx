import React, { useEffect } from "react";
import { Box, ToggleButton, ToggleButtonGroup, Tooltip } from "@mui/material";
import { useLanguageStore } from "../../store/language";
import { useTranslation } from "react-i18next";

interface LanguageSwitcherProps {
  variant?: "text" | "icon";
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = "text",
}) => {
  const { currentLanguage, setLanguage } = useLanguageStore();
  const { i18n } = useTranslation();

  useEffect(() => {
    if (currentLanguage) {
      i18n.changeLanguage(currentLanguage);
    }
  }, [currentLanguage, i18n]);

  const handleLanguageChange = (
    _event: React.MouseEvent<HTMLElement>,
    newLanguage: string | null
  ) => {
    if (newLanguage !== null) {
      setLanguage(newLanguage);
      i18n.changeLanguage(newLanguage);
    }
  };

  return (
    <Box>
      <ToggleButtonGroup
        value={currentLanguage}
        exclusive
        onChange={handleLanguageChange}
        aria-label="language switcher"
        size={variant === "icon" ? "small" : "medium"}
        sx={{
          backgroundColor: "#e3f2fd", // light blue background for the group
          borderRadius: "8px",
          height: variant === "icon" ? 36 : "auto",
          "& .MuiToggleButton-root": {
            color: "#1976d2", // blue text for unselected
            borderColor: "#90caf9",
            backgroundColor: "#e3f2fd", // same as group for unselected
            "&:hover": {
              backgroundColor: "#bbdefb",
            },
          },
          "& .Mui-selected": {
            backgroundColor: "#1976d2",
            color: "#fff",
            "&:hover": {
              backgroundColor: "#1565c0",
            },
          },
        }}
      >
        <ToggleButton value="en" aria-label="English">
          {variant === "icon" ? (
            <Tooltip title="English">
              <Box
                component="span"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: 24,
                  height: 24,
                }}
              >
                EN
              </Box>
            </Tooltip>
          ) : (
            "EN"
          )}
        </ToggleButton>
        <ToggleButton value="de" aria-label="German">
          {variant === "icon" ? (
            <Tooltip title="Deutsch">
              <Box
                component="span"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: 24,
                  height: 24,
                }}
              >
                DE
              </Box>
            </Tooltip>
          ) : (
            "DE"
          )}
        </ToggleButton>
      </ToggleButtonGroup>
    </Box>
  );
};

export default LanguageSwitcher;
