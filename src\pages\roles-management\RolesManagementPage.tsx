import { useEffect } from "react";
import { Box } from "@mui/material";
import { useNavigate, useLocation, Routes, Route } from "react-router-dom";
import { useAuthStore } from "../../store/auth";
import RolesPage from "../roles/RolesPage";
import PermissionsPage from "../permissions/PermissionsPage";
import AssignUserRoles from "../userRoles/AssiagnuserRole";
import AssignRolePermissions from "../permissions/RolePermissionsPage";
import RoleAuditHistory from "../roles/roleAuditHistory";

function RolesManagementPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuthStore();

  useEffect(() => {
    // If we're on the parent route without a child, redirect to roles
    if (location.pathname === "/roles-management" || location.pathname === "/roles-management/") {
      navigate("/roles-management/roles", { replace: true });
    }
  }, [location.pathname, navigate]);

  if (user?.role !== "business") {
    return null;
  }

  return (
    <Box>
      <Routes>
        <Route path="roles" element={<RolesPage />} />
        <Route path="permissions" element={<PermissionsPage />} />
        <Route path="user-roles" element={<AssignUserRoles />} />
        <Route path="role-permissions" element={<AssignRolePermissions />} />
          <Route path="role-audit-history" element={<RoleAuditHistory />} />

      </Routes>
    </Box>
  );
}

export default RolesManagementPage;
